config CAPI_TRACE
	bool "CAPI trace support"
	default y
	help
	  If you say Y here, the kernelcapi driver can make verbose traces
	  of CAPI messages. This feature can be enabled/disabled via IOCTL for
	  every controller (default disabled).
	  This will increase the size of the kernelcapi module by 20 KB.
	  If unsure, say Y.

config ISDN_CAPI_CAPI20
	tristate "CAPI2.0 /dev/capi20 support"
	help
	  This option will provide the CAPI 2.0 interface to userspace
	  applications via /dev/capi20. Applications should use the
	  standardized libcapi20 to access this functionality.  You should say
	  Y/M here.

config ISDN_CAPI_MIDDLEWARE
	bool "CAPI2.0 Middleware support"
	depends on ISDN_CAPI_CAPI20 && TTY
	help
	  This option will enhance the capabilities of the /dev/capi20
	  interface.  It will provide a means of moving a data connection,
	  established via the usual /dev/capi20 interface to a special tty
	  device.  If you want to use pppd with pppdcapiplugin to dial up to
	  your ISP, say Y here.

config ISDN_CAPI_CAPIDRV
	tristate "CAPI2.0 capidrv interface support"
	depends on ISDN_I4L
	help
	  This option provides the glue code to hook up CAPI driven cards to
	  the legacy isdn4linux link layer.  If you have a card which is
	  supported by a CAPI driver, but still want to use old features like
	  ippp interfaces or ttyI emulation, say Y/M here.

config ISDN_CAPI_CAPIDRV_VERBOSE
	bool "Verbose reason code reporting"
	depends on ISDN_CAPI_CAPIDRV
	help
	  If you say Y here, the capidrv interface will give verbose reasons
	  for disconnecting. This will increase the size of the kernel by 7 KB.
	  If unsure, say N.
