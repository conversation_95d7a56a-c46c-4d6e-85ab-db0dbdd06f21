# SPDX-License-Identifier: GPL-2.0
obj-$(CONFIG_IRQCHIP)			+= irqchip.o

obj-$(CONFIG_ALPINE_MSI)		+= irq-alpine-msi.o
obj-$(CONFIG_ATH79)			+= irq-ath79-cpu.o
obj-$(CONFIG_ATH79)			+= irq-ath79-misc.o
obj-$(CONFIG_ARCH_BCM2835)		+= irq-bcm2835.o
obj-$(CONFIG_ARCH_BCM2835)		+= irq-bcm2836.o
obj-$(CONFIG_ARCH_EXYNOS)		+= exynos-combiner.o
obj-$(CONFIG_FARADAY_FTINTC010)		+= irq-ftintc010.o
obj-$(CONFIG_ARCH_HIP04)		+= irq-hip04.o
obj-$(CONFIG_ARCH_LPC32XX)		+= irq-lpc32xx.o
obj-$(CONFIG_ARCH_MMP)			+= irq-mmp.o
obj-$(CONFIG_IRQ_MXS)			+= irq-mxs.o
obj-$(CONFIG_ARCH_TEGRA)		+= irq-tegra.o
obj-$(CONFIG_ARCH_S3C24XX)		+= irq-s3c24xx.o
obj-$(CONFIG_DW_APB_ICTL)		+= irq-dw-apb-ictl.o
obj-$(CONFIG_CLPS711X_IRQCHIP)		+= irq-clps711x.o
obj-$(CONFIG_OMPIC)			+= irq-ompic.o
obj-$(CONFIG_OR1K_PIC)			+= irq-or1k-pic.o
obj-$(CONFIG_ORION_IRQCHIP)		+= irq-orion.o
obj-$(CONFIG_OMAP_IRQCHIP)		+= irq-omap-intc.o
obj-$(CONFIG_ARCH_SUNXI)		+= irq-sun4i.o
obj-$(CONFIG_ARCH_SUNXI)		+= irq-sunxi-nmi.o
obj-$(CONFIG_ARCH_SPEAR3XX)		+= spear-shirq.o
obj-$(CONFIG_ARM_GIC)			+= irq-gic.o irq-gic-common.o
obj-$(CONFIG_ARM_GIC_PM)		+= irq-gic-pm.o
obj-$(CONFIG_ARCH_REALVIEW)		+= irq-gic-realview.o
obj-$(CONFIG_ARM_GIC_V2M)		+= irq-gic-v2m.o
obj-$(CONFIG_ARM_GIC_V3)		+= irq-gic-v3.o irq-gic-v3-mbi.o irq-gic-common.o
obj-$(CONFIG_ARM_GIC_V3_ITS)		+= irq-gic-v3-its.o irq-gic-v3-its-platform-msi.o irq-gic-v4.o
obj-$(CONFIG_ARM_GIC_V3_ITS_PCI)	+= irq-gic-v3-its-pci-msi.o
obj-$(CONFIG_ARM_GIC_V3_ITS_FSL_MC)	+= irq-gic-v3-its-fsl-mc-msi.o
obj-$(CONFIG_PARTITION_PERCPU)		+= irq-partition-percpu.o
obj-$(CONFIG_HISILICON_IRQ_MBIGEN)	+= irq-mbigen.o
obj-$(CONFIG_ARM_NVIC)			+= irq-nvic.o
obj-$(CONFIG_ARM_VIC)			+= irq-vic.o
obj-$(CONFIG_ARMADA_370_XP_IRQ)		+= irq-armada-370-xp.o
obj-$(CONFIG_ATMEL_AIC_IRQ)		+= irq-atmel-aic-common.o irq-atmel-aic.o
obj-$(CONFIG_ATMEL_AIC5_IRQ)	+= irq-atmel-aic-common.o irq-atmel-aic5.o
obj-$(CONFIG_I8259)			+= irq-i8259.o
obj-$(CONFIG_IMGPDC_IRQ)		+= irq-imgpdc.o
obj-$(CONFIG_IRQ_MIPS_CPU)		+= irq-mips-cpu.o
obj-$(CONFIG_IRQ_LOONGARCH_CPU)		+= irq-loongarch-cpu.o
obj-$(CONFIG_LOONGSON_LIOINTC)		+= irq-loongson-liointc.o
obj-$(CONFIG_LOONGSON_HTVEC)		+= irq-loongson-htvec.o
obj-$(CONFIG_LOONGSON_EXTIOI)		+= irq-loongson-extioi.o
obj-$(CONFIG_LOONGARCH_EXTIOI)		+= irq-loongarch-extioi.o
obj-$(CONFIG_LOONGSON_PCH_MSI)		+= irq-loongson-pch-msi.o
obj-$(CONFIG_LOONGSON_PCH_PIC)		+= irq-loongson-pch-pic.o
obj-$(CONFIG_LOONGSON_PCH_LPC)		+= irq-loongson-pch-lpc.o
obj-$(CONFIG_LOONGSON_MACH2K)		+= irq-loongson-2k1000-iointc.o irq-loongson-2k1000-msi.o
obj-$(CONFIG_LOONGSON2_LIOINTC)		+= irq-loongarch-2k-iointc.o
obj-$(CONFIG_LOONGSON2_MSI)		+= irq-loongarch-2k1000-msi.o

obj-$(CONFIG_SIRF_IRQ)			+= irq-sirfsoc.o
obj-$(CONFIG_JCORE_AIC)			+= irq-jcore-aic.o
obj-$(CONFIG_RENESAS_INTC_IRQPIN)	+= irq-renesas-intc-irqpin.o
obj-$(CONFIG_RENESAS_IRQC)		+= irq-renesas-irqc.o
obj-$(CONFIG_VERSATILE_FPGA_IRQ)	+= irq-versatile-fpga.o
obj-$(CONFIG_ARCH_NSPIRE)		+= irq-zevio.o
obj-$(CONFIG_ARCH_VT8500)		+= irq-vt8500.o
obj-$(CONFIG_ST_IRQCHIP)		+= irq-st.o
obj-$(CONFIG_TANGO_IRQ)			+= irq-tango.o
obj-$(CONFIG_TB10X_IRQC)		+= irq-tb10x.o
obj-$(CONFIG_TS4800_IRQ)		+= irq-ts4800.o
obj-$(CONFIG_XTENSA)			+= irq-xtensa-pic.o
obj-$(CONFIG_XTENSA_MX)			+= irq-xtensa-mx.o
obj-$(CONFIG_XILINX_INTC)		+= irq-xilinx-intc.o
obj-$(CONFIG_IRQ_CROSSBAR)		+= irq-crossbar.o
obj-$(CONFIG_SOC_VF610)			+= irq-vf610-mscm-ir.o
obj-$(CONFIG_BCM6345_L1_IRQ)		+= irq-bcm6345-l1.o
obj-$(CONFIG_BCM7038_L1_IRQ)		+= irq-bcm7038-l1.o
obj-$(CONFIG_BCM7120_L2_IRQ)		+= irq-bcm7120-l2.o
obj-$(CONFIG_BRCMSTB_L2_IRQ)		+= irq-brcmstb-l2.o
obj-$(CONFIG_KEYSTONE_IRQ)		+= irq-keystone.o
obj-$(CONFIG_MIPS_GIC)			+= irq-mips-gic.o
obj-$(CONFIG_ARCH_MEDIATEK)		+= irq-mtk-sysirq.o irq-mtk-cirq.o
obj-$(CONFIG_ARCH_DIGICOLOR)		+= irq-digicolor.o
obj-$(CONFIG_RENESAS_H8300H_INTC)	+= irq-renesas-h8300h.o
obj-$(CONFIG_RENESAS_H8S_INTC)		+= irq-renesas-h8s.o
obj-$(CONFIG_ARCH_SA1100)		+= irq-sa11x0.o
obj-$(CONFIG_INGENIC_IRQ)		+= irq-ingenic.o
obj-$(CONFIG_IMX_GPCV2)			+= irq-imx-gpcv2.o
obj-$(CONFIG_PIC32_EVIC)		+= irq-pic32-evic.o
obj-$(CONFIG_MSCC_OCELOT_IRQ)		+= irq-mscc-ocelot.o
obj-$(CONFIG_MVEBU_GICP)		+= irq-mvebu-gicp.o
obj-$(CONFIG_MVEBU_ICU)			+= irq-mvebu-icu.o
obj-$(CONFIG_MVEBU_ODMI)		+= irq-mvebu-odmi.o
obj-$(CONFIG_MVEBU_PIC)			+= irq-mvebu-pic.o
obj-$(CONFIG_LS_SCFG_MSI)		+= irq-ls-scfg-msi.o
obj-$(CONFIG_EZNPS_GIC)			+= irq-eznps.o
obj-$(CONFIG_ARCH_ASPEED)		+= irq-aspeed-vic.o irq-aspeed-i2c-ic.o
obj-$(CONFIG_STM32_EXTI) 		+= irq-stm32-exti.o
obj-$(CONFIG_QCOM_IRQ_COMBINER)		+= qcom-irq-combiner.o
obj-$(CONFIG_IRQ_UNIPHIER_AIDET)	+= irq-uniphier-aidet.o
obj-$(CONFIG_ARCH_SYNQUACER)		+= irq-sni-exiu.o
obj-$(CONFIG_MESON_IRQ_GPIO)		+= irq-meson-gpio.o
obj-$(CONFIG_GOLDFISH_PIC) 		+= irq-goldfish-pic.o
obj-$(CONFIG_NDS32)			+= irq-ativic32.o
obj-$(CONFIG_QCOM_PDC)			+= qcom-pdc.o
obj-$(CONFIG_SIFIVE_PLIC)		+= irq-sifive-plic.o
