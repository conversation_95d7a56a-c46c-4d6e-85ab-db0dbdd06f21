menu "IRQ chip support"

config IRQCHIP
	def_bool y
	depends on OF_IRQ

config ARM_GIC
	bool
	select IRQ_DOMAIN
	select IRQ_DOMAIN_HIERARCHY
	select GENERIC_IRQ_MULTI_HANDLER
	select GENERIC_IRQ_EFFECTIVE_AFF_MASK

config ARM_GIC_PM
	bool
	depends on PM
	select ARM_GIC
	select PM_CLK

config ARM_GIC_MAX_NR
	int
	default 2 if ARCH_REALVIEW
	default 1

config ARM_GIC_V2M
	bool
	depends on PCI
	select ARM_GIC
	select PCI_MSI

config GIC_NON_BANKED
	bool

config ARM_GIC_V3
	bool
	select IRQ_DOMAIN
	select GENERIC_IRQ_MULTI_HANDLER
	select IRQ_DOMAIN_HIERARCHY
	select PARTITION_PERCPU
	select GENERIC_IRQ_EFFECTIVE_AFF_MASK

config ARM_GIC_V3_ITS
	bool
	select GENERIC_MSI_IRQ_DOMAIN
	default ARM_GIC_V3

config ARM_GIC_V3_ITS_PCI
	bool
	depends on ARM_GIC_V3_ITS
	depends on PCI
	depends on PCI_MSI
	default ARM_GIC_V3_ITS

config ARM_GIC_V3_ITS_FSL_MC
	bool
	depends on ARM_GIC_V3_ITS
	depends on FSL_MC_BUS
	default ARM_GIC_V3_ITS

config ARM_NVIC
	bool
	select IRQ_DOMAIN
	select IRQ_DOMAIN_HIERARCHY
	select GENERIC_IRQ_CHIP

config ARM_VIC
	bool
	select IRQ_DOMAIN
	select GENERIC_IRQ_MULTI_HANDLER

config ARM_VIC_NR
	int
	default 4 if ARCH_S5PV210
	default 2
	depends on ARM_VIC
	help
	  The maximum number of VICs available in the system, for
	  power management.

config ARMADA_370_XP_IRQ
	bool
	select GENERIC_IRQ_CHIP
	select PCI_MSI if PCI
	select GENERIC_IRQ_EFFECTIVE_AFF_MASK

config ALPINE_MSI
	bool
	depends on PCI
	select PCI_MSI
	select GENERIC_IRQ_CHIP

config ATMEL_AIC_IRQ
	bool
	select GENERIC_IRQ_CHIP
	select IRQ_DOMAIN
	select GENERIC_IRQ_MULTI_HANDLER
	select SPARSE_IRQ

config ATMEL_AIC5_IRQ
	bool
	select GENERIC_IRQ_CHIP
	select IRQ_DOMAIN
	select GENERIC_IRQ_MULTI_HANDLER
	select SPARSE_IRQ

config I8259
	bool
	select IRQ_DOMAIN

config BCM6345_L1_IRQ
	bool
	select GENERIC_IRQ_CHIP
	select IRQ_DOMAIN
	select GENERIC_IRQ_EFFECTIVE_AFF_MASK

config BCM7038_L1_IRQ
	bool
	select GENERIC_IRQ_CHIP
	select IRQ_DOMAIN
	select GENERIC_IRQ_EFFECTIVE_AFF_MASK

config BCM7120_L2_IRQ
	bool
	select GENERIC_IRQ_CHIP
	select IRQ_DOMAIN

config BRCMSTB_L2_IRQ
	bool
	select GENERIC_IRQ_CHIP
	select IRQ_DOMAIN

config DW_APB_ICTL
	bool
	select GENERIC_IRQ_CHIP
	select IRQ_DOMAIN

config FARADAY_FTINTC010
	bool
	select IRQ_DOMAIN
	select GENERIC_IRQ_MULTI_HANDLER
	select SPARSE_IRQ

config HISILICON_IRQ_MBIGEN
	bool
	select ARM_GIC_V3
	select ARM_GIC_V3_ITS

config IMGPDC_IRQ
	bool
	select GENERIC_IRQ_CHIP
	select IRQ_DOMAIN

config IRQ_MIPS_CPU
	bool
	select GENERIC_IRQ_CHIP
	select GENERIC_IRQ_IPI if SYS_SUPPORTS_MULTITHREADING
	select IRQ_DOMAIN
	select IRQ_DOMAIN_HIERARCHY if GENERIC_IRQ_IPI
	select GENERIC_IRQ_EFFECTIVE_AFF_MASK

config IRQ_LOONGARCH_CPU
	bool
	select GENERIC_IRQ_CHIP
	select IRQ_DOMAIN
	select GENERIC_IRQ_EFFECTIVE_AFF_MASK

config LOONGSON2_LIOINTC
	bool "Loongson2 SOC Series LIOINTC IRQ controller"
	select GENERIC_IRQ_CHIP
	select IRQ_DOMAIN
	select GENERIC_IRQ_EFFECTIVE_AFF_MASK

config LOONGSON2_MSI
	bool "Loongson2 SOC Series MSI IRQ controller"
	select GENERIC_IRQ_CHIP
	select IRQ_DOMAIN
	select GENERIC_IRQ_EFFECTIVE_AFF_MASK

config CLPS711X_IRQCHIP
	bool
	depends on ARCH_CLPS711X
	select IRQ_DOMAIN
	select GENERIC_IRQ_MULTI_HANDLER
	select SPARSE_IRQ
	default y

config OMPIC
	bool

config OR1K_PIC
	bool
	select IRQ_DOMAIN

config OMAP_IRQCHIP
	bool
	select GENERIC_IRQ_CHIP
	select IRQ_DOMAIN

config ORION_IRQCHIP
	bool
	select IRQ_DOMAIN
	select GENERIC_IRQ_MULTI_HANDLER

config PIC32_EVIC
	bool
	select GENERIC_IRQ_CHIP
	select IRQ_DOMAIN

config JCORE_AIC
	bool "J-Core integrated AIC" if COMPILE_TEST
	depends on OF
	select IRQ_DOMAIN
	help
	  Support for the J-Core integrated AIC.

config RENESAS_INTC_IRQPIN
	bool
	select IRQ_DOMAIN

config RENESAS_IRQC
	bool
	select GENERIC_IRQ_CHIP
	select IRQ_DOMAIN

config ST_IRQCHIP
	bool
	select REGMAP
	select MFD_SYSCON
	help
	  Enables SysCfg Controlled IRQs on STi based platforms.

config TANGO_IRQ
	bool
	select IRQ_DOMAIN
	select GENERIC_IRQ_CHIP

config TB10X_IRQC
	bool
	select IRQ_DOMAIN
	select GENERIC_IRQ_CHIP

config TS4800_IRQ
	tristate "TS-4800 IRQ controller"
	select IRQ_DOMAIN
	depends on HAS_IOMEM
	depends on SOC_IMX51 || COMPILE_TEST
	help
	  Support for the TS-4800 FPGA IRQ controller

config VERSATILE_FPGA_IRQ
	bool
	select IRQ_DOMAIN

config VERSATILE_FPGA_IRQ_NR
       int
       default 4
       depends on VERSATILE_FPGA_IRQ

config XTENSA_MX
	bool
	select IRQ_DOMAIN
	select GENERIC_IRQ_EFFECTIVE_AFF_MASK

config XILINX_INTC
	bool
	select IRQ_DOMAIN

config IRQ_CROSSBAR
	bool
	help
	  Support for a CROSSBAR ip that precedes the main interrupt controller.
	  The primary irqchip invokes the crossbar's callback which inturn allocates
	  a free irq and configures the IP. Thus the peripheral interrupts are
	  routed to one of the free irqchip interrupt lines.

config KEYSTONE_IRQ
	tristate "Keystone 2 IRQ controller IP"
	depends on ARCH_KEYSTONE
	help
		Support for Texas Instruments Keystone 2 IRQ controller IP which
		is part of the Keystone 2 IPC mechanism

config MIPS_GIC
	bool
	select GENERIC_IRQ_IPI
	select IRQ_DOMAIN_HIERARCHY
	select MIPS_CM

config INGENIC_IRQ
	bool
	depends on MACH_INGENIC
	default y

config RENESAS_H8300H_INTC
        bool
	select IRQ_DOMAIN

config RENESAS_H8S_INTC
        bool
	select IRQ_DOMAIN

config IMX_GPCV2
	bool
	select IRQ_DOMAIN
	help
	  Enables the wakeup IRQs for IMX platforms with GPCv2 block

config IRQ_MXS
	def_bool y if MACH_ASM9260 || ARCH_MXS
	select IRQ_DOMAIN
	select STMP_DEVICE

config MSCC_OCELOT_IRQ
	bool
	select IRQ_DOMAIN
	select GENERIC_IRQ_CHIP

config MVEBU_GICP
	bool

config MVEBU_ICU
	bool

config MVEBU_ODMI
	bool
	select GENERIC_MSI_IRQ_DOMAIN

config MVEBU_PIC
	bool

config LS_SCFG_MSI
	def_bool y if SOC_LS1021A || ARCH_LAYERSCAPE
	depends on PCI && PCI_MSI

config PARTITION_PERCPU
	bool

config EZNPS_GIC
	bool "NPS400 Global Interrupt Manager (GIM)"
	depends on ARC || (COMPILE_TEST && !64BIT)
	select IRQ_DOMAIN
	help
	  Support the EZchip NPS400 global interrupt controller

config STM32_EXTI
	bool
	select IRQ_DOMAIN
	select GENERIC_IRQ_CHIP

config QCOM_IRQ_COMBINER
	bool "QCOM IRQ combiner support"
	depends on ARCH_QCOM && ACPI
	select IRQ_DOMAIN
	select IRQ_DOMAIN_HIERARCHY
	help
	  Say yes here to add support for the IRQ combiner devices embedded
	  in Qualcomm Technologies chips.

config IRQ_UNIPHIER_AIDET
	bool "UniPhier AIDET support" if COMPILE_TEST
	depends on ARCH_UNIPHIER || COMPILE_TEST
	default ARCH_UNIPHIER
	select IRQ_DOMAIN_HIERARCHY
	help
	  Support for the UniPhier AIDET (ARM Interrupt Detector).

config MESON_IRQ_GPIO
       bool "Meson GPIO Interrupt Multiplexer"
       depends on ARCH_MESON
       select IRQ_DOMAIN
       select IRQ_DOMAIN_HIERARCHY
       help
         Support Meson SoC Family GPIO Interrupt Multiplexer

config GOLDFISH_PIC
       bool "Goldfish programmable interrupt controller"
       depends on MIPS && (GOLDFISH || COMPILE_TEST)
       select IRQ_DOMAIN
       help
         Say yes here to enable Goldfish interrupt controller driver used
         for Goldfish based virtual platforms.

config QCOM_PDC
	bool "QCOM PDC"
	depends on ARCH_QCOM
	select IRQ_DOMAIN
	select IRQ_DOMAIN_HIERARCHY
	help
	  Power Domain Controller driver to manage and configure wakeup
	  IRQs for Qualcomm Technologies Inc (QTI) mobile chips.

config LOONGSON_LIOINTC
	bool "Loongson Local I/O Interrupt Controller"
	depends on MACH_LOONGSON64
	default y
	select IRQ_DOMAIN
	select GENERIC_IRQ_CHIP
	help
	  Support for the Loongson Local I/O Interrupt Controller.

config LOONGSON_PCH_MSI
	bool "Loongson PCH MSI Controller"
	depends on MACH_LOONGSON64 && !CPU_LOONGSON2K || COMPILE_TEST
	depends on PCI
	default MACH_LOONGSON64
	select IRQ_DOMAIN_HIERARCHY
	select PCI_MSI
	select PCI_MSI_IRQ_DOMAIN
	help
	  Support for the Loongson PCH MSI Controller.

config LOONGSON_HTVEC
	bool "Loongson3 HyperTransport Interrupt Vector Controller"
	depends on MACH_LOONGSON64 || COMPILE_TEST
	default MACH_LOONGSON64
	select IRQ_DOMAIN_HIERARCHY
	help
	  Support for the Loongson3 HyperTransport Interrupt Vector Controller.

config LOONGSON_EXTIOI
	bool "Loongson3 Extend I/O Interrupt Vector Controller"
	depends on IRQ_MIPS_CPU || COMPILE_TEST
	default MACH_LOONGSON64
	select IRQ_DOMAIN_HIERARCHY
	help
	  Support for the Loongson3 Extend I/O Interrupt Vector Controller.

config LOONGARCH_EXTIOI
	bool "Loongson3 Extend I/O Interrupt Vector Controller"
	depends on IRQ_LOONGARCH_CPU || COMPILE_TEST
	default MACH_LOONGSON64
	select IRQ_DOMAIN_HIERARCHY
	select REGMAP
	select MFD_SYSCON
	help
	  Support for the Loongson3 Extend I/O Interrupt Vector Controller.

config LOONGSON_PCH_LPC
	bool "Loongson PCH LPC Controller"
	depends on MACH_LOONGSON64 || COMPILE_TEST
	depends on PCI
	default MACH_LOONGSON64
	select IRQ_DOMAIN_HIERARCHY
	help
	  Support for the Loongson PCH LPC Controller.

config LOONGSON_PCH_PIC
	bool "Loongson PCH PIC Controller"
	depends on MACH_LOONGSON64 || COMPILE_TEST
	default MACH_LOONGSON64
	select IRQ_DOMAIN_HIERARCHY
	select IRQ_FASTEOI_HIERARCHY_HANDLERS
	help
	  Support for the Loongson PCH PIC Controller.

config SIFIVE_PLIC
	bool "SiFive Platform-Level Interrupt Controller"
	depends on RISCV
	help
	   This enables support for the PLIC chip found in SiFive (and
	   potentially other) RISC-V systems.  The PLIC controls devices
	   interrupts and connects them to each core's local interrupt
	   controller.  Aside from timer and software interrupts, all other
	   interrupt sources are subordinate to the PLIC.

	   If you don't know what to do here, say Y.

endmenu
