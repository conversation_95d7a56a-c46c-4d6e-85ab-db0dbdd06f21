/*
 * stv0900_reg.h
 *
 * Driver for ST STV0900 satellite demodulator IC.
 *
 * Copyright (C) ST Microelectronics.
 * Copyright (C) 2009 NetUP Inc.
 * Copyright (C) 2009 Igor <PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 *
 * GNU General Public License for more details.
 */

#ifndef STV0900_REG_H
#define STV0900_REG_H

extern s32 shiftx(s32 x, int demod, s32 shift);

#define REGx(x) shiftx(x, demod, 0x200)
#define FLDx(x) shiftx(x, demod, 0x2000000)

/*MID*/
#define R0900_MID 0xf100
#define F0900_MCHIP_IDENT 0xf10000f0
#define F0900_MRELEASE 0xf100000f

/*DACR1*/
#define R0900_DACR1 0xf113
#define F0900_DAC_MODE 0xf11300e0
#define F0900_DAC_VALUE1 0xf113000f

/*DACR2*/
#define R0900_DACR2 0xf114
#define F0900_DAC_VALUE0 0xf11400ff

/*OUTCFG*/
#define R0900_OUTCFG 0xf11c
#define F0900_OUTSERRS1_HZ 0xf11c0040
#define F0900_OUTSERRS2_HZ 0xf11c0020
#define F0900_OUTSERRS3_HZ 0xf11c0010
#define F0900_OUTPARRS3_HZ 0xf11c0008

/*IRQSTATUS3*/
#define R0900_IRQSTATUS3 0xf120
#define F0900_SPLL_LOCK 0xf1200020
#define F0900_SSTREAM_LCK_3 0xf1200010
#define F0900_SSTREAM_LCK_2 0xf1200008
#define F0900_SSTREAM_LCK_1 0xf1200004
#define F0900_SDVBS1_PRF_2 0xf1200002
#define F0900_SDVBS1_PRF_1 0xf1200001

/*IRQSTATUS2*/
#define R0900_IRQSTATUS2 0xf121
#define F0900_SSPY_ENDSIM_3 0xf1210080
#define F0900_SSPY_ENDSIM_2 0xf1210040
#define F0900_SSPY_ENDSIM_1 0xf1210020
#define F0900_SPKTDEL_ERROR_2 0xf1210010
#define F0900_SPKTDEL_LOCKB_2 0xf1210008
#define F0900_SPKTDEL_LOCK_2 0xf1210004
#define F0900_SPKTDEL_ERROR_1 0xf1210002
#define F0900_SPKTDEL_LOCKB_1 0xf1210001

/*IRQSTATUS1*/
#define R0900_IRQSTATUS1 0xf122
#define F0900_SPKTDEL_LOCK_1 0xf1220080
#define F0900_SDEMOD_LOCKB_2 0xf1220004
#define F0900_SDEMOD_LOCK_2 0xf1220002
#define F0900_SDEMOD_IRQ_2 0xf1220001

/*IRQSTATUS0*/
#define R0900_IRQSTATUS0 0xf123
#define F0900_SDEMOD_LOCKB_1 0xf1230080
#define F0900_SDEMOD_LOCK_1 0xf1230040
#define F0900_SDEMOD_IRQ_1 0xf1230020
#define F0900_SBCH_ERRFLAG 0xf1230010
#define F0900_SDISEQC2RX_IRQ 0xf1230008
#define F0900_SDISEQC2TX_IRQ 0xf1230004
#define F0900_SDISEQC1RX_IRQ 0xf1230002
#define F0900_SDISEQC1TX_IRQ 0xf1230001

/*IRQMASK3*/
#define R0900_IRQMASK3 0xf124
#define F0900_MPLL_LOCK 0xf1240020
#define F0900_MSTREAM_LCK_3 0xf1240010
#define F0900_MSTREAM_LCK_2 0xf1240008
#define F0900_MSTREAM_LCK_1 0xf1240004
#define F0900_MDVBS1_PRF_2 0xf1240002
#define F0900_MDVBS1_PRF_1 0xf1240001

/*IRQMASK2*/
#define R0900_IRQMASK2 0xf125
#define F0900_MSPY_ENDSIM_3 0xf1250080
#define F0900_MSPY_ENDSIM_2 0xf1250040
#define F0900_MSPY_ENDSIM_1 0xf1250020
#define F0900_MPKTDEL_ERROR_2 0xf1250010
#define F0900_MPKTDEL_LOCKB_2 0xf1250008
#define F0900_MPKTDEL_LOCK_2 0xf1250004
#define F0900_MPKTDEL_ERROR_1 0xf1250002
#define F0900_MPKTDEL_LOCKB_1 0xf1250001

/*IRQMASK1*/
#define R0900_IRQMASK1 0xf126
#define F0900_MPKTDEL_LOCK_1 0xf1260080
#define F0900_MEXTPINB2 0xf1260040
#define F0900_MEXTPIN2 0xf1260020
#define F0900_MEXTPINB1 0xf1260010
#define F0900_MEXTPIN1 0xf1260008
#define F0900_MDEMOD_LOCKB_2 0xf1260004
#define F0900_MDEMOD_LOCK_2 0xf1260002
#define F0900_MDEMOD_IRQ_2 0xf1260001

/*IRQMASK0*/
#define R0900_IRQMASK0 0xf127
#define F0900_MDEMOD_LOCKB_1 0xf1270080
#define F0900_MDEMOD_LOCK_1 0xf1270040
#define F0900_MDEMOD_IRQ_1 0xf1270020
#define F0900_MBCH_ERRFLAG 0xf1270010
#define F0900_MDISEQC2RX_IRQ 0xf1270008
#define F0900_MDISEQC2TX_IRQ 0xf1270004
#define F0900_MDISEQC1RX_IRQ 0xf1270002
#define F0900_MDISEQC1TX_IRQ 0xf1270001

/*I2CCFG*/
#define R0900_I2CCFG 0xf129
#define F0900_I2C_FASTMODE 0xf1290008
#define F0900_I2CADDR_INC 0xf1290003

/*P1_I2CRPT*/
#define R0900_P1_I2CRPT 0xf12a
#define I2CRPT shiftx(R0900_P1_I2CRPT, demod, -1)
#define F0900_P1_I2CT_ON 0xf12a0080
#define I2CT_ON shiftx(F0900_P1_I2CT_ON, demod, -0x10000)
#define F0900_P1_ENARPT_LEVEL 0xf12a0070
#define F0900_P1_SCLT_DELAY 0xf12a0008
#define F0900_P1_STOP_ENABLE 0xf12a0004
#define F0900_P1_STOP_SDAT2SDA 0xf12a0002

/*P2_I2CRPT*/
#define R0900_P2_I2CRPT 0xf12b
#define F0900_P2_I2CT_ON 0xf12b0080
#define F0900_P2_ENARPT_LEVEL 0xf12b0070
#define F0900_P2_SCLT_DELAY 0xf12b0008
#define F0900_P2_STOP_ENABLE 0xf12b0004
#define F0900_P2_STOP_SDAT2SDA 0xf12b0002

/*IOPVALUE6*/
#define R0900_IOPVALUE6 0xf138
#define F0900_VSCL 0xf1380004
#define F0900_VSDA 0xf1380002
#define F0900_VDATA3_0 0xf1380001

/*IOPVALUE5*/
#define R0900_IOPVALUE5 0xf139
#define F0900_VDATA3_1 0xf1390080
#define F0900_VDATA3_2 0xf1390040
#define F0900_VDATA3_3 0xf1390020
#define F0900_VDATA3_4 0xf1390010
#define F0900_VDATA3_5 0xf1390008
#define F0900_VDATA3_6 0xf1390004
#define F0900_VDATA3_7 0xf1390002
#define F0900_VCLKOUT3 0xf1390001

/*IOPVALUE4*/
#define R0900_IOPVALUE4 0xf13a
#define F0900_VSTROUT3 0xf13a0080
#define F0900_VDPN3 0xf13a0040
#define F0900_VERROR3 0xf13a0020
#define F0900_VDATA2_7 0xf13a0010
#define F0900_VCLKOUT2 0xf13a0008
#define F0900_VSTROUT2 0xf13a0004
#define F0900_VDPN2 0xf13a0002
#define F0900_VERROR2 0xf13a0001

/*IOPVALUE3*/
#define R0900_IOPVALUE3 0xf13b
#define F0900_VDATA1_7 0xf13b0080
#define F0900_VCLKOUT1 0xf13b0040
#define F0900_VSTROUT1 0xf13b0020
#define F0900_VDPN1 0xf13b0010
#define F0900_VERROR1 0xf13b0008
#define F0900_VCLKOUT27 0xf13b0004
#define F0900_VDISEQCOUT2 0xf13b0002
#define F0900_VSCLT2 0xf13b0001

/*IOPVALUE2*/
#define R0900_IOPVALUE2 0xf13c
#define F0900_VSDAT2 0xf13c0080
#define F0900_VAGCRF2 0xf13c0040
#define F0900_VDISEQCOUT1 0xf13c0020
#define F0900_VSCLT1 0xf13c0010
#define F0900_VSDAT1 0xf13c0008
#define F0900_VAGCRF1 0xf13c0004
#define F0900_VDIRCLK 0xf13c0002
#define F0900_VSTDBY 0xf13c0001

/*IOPVALUE1*/
#define R0900_IOPVALUE1 0xf13d
#define F0900_VCS1 0xf13d0080
#define F0900_VCS0 0xf13d0040
#define F0900_VGPIO13 0xf13d0020
#define F0900_VGPIO12 0xf13d0010
#define F0900_VGPIO11 0xf13d0008
#define F0900_VGPIO10 0xf13d0004
#define F0900_VGPIO9 0xf13d0002
#define F0900_VGPIO8 0xf13d0001

/*IOPVALUE0*/
#define R0900_IOPVALUE0 0xf13e
#define F0900_VGPIO7 0xf13e0080
#define F0900_VGPIO6 0xf13e0040
#define F0900_VGPIO5 0xf13e0020
#define F0900_VGPIO4 0xf13e0010
#define F0900_VGPIO3 0xf13e0008
#define F0900_VGPIO2 0xf13e0004
#define F0900_VGPIO1 0xf13e0002
#define F0900_VCLKI2 0xf13e0001

/*CLKI2CFG*/
#define R0900_CLKI2CFG 0xf140
#define F0900_CLKI2_OPD 0xf1400080
#define F0900_CLKI2_CONFIG 0xf140007e
#define F0900_CLKI2_XOR 0xf1400001

/*GPIO1CFG*/
#define R0900_GPIO1CFG 0xf141
#define F0900_GPIO1_OPD 0xf1410080
#define F0900_GPIO1_CONFIG 0xf141007e
#define F0900_GPIO1_XOR 0xf1410001

/*GPIO2CFG*/
#define R0900_GPIO2CFG 0xf142
#define F0900_GPIO2_OPD 0xf1420080
#define F0900_GPIO2_CONFIG 0xf142007e
#define F0900_GPIO2_XOR 0xf1420001

/*GPIO3CFG*/
#define R0900_GPIO3CFG 0xf143
#define F0900_GPIO3_OPD 0xf1430080
#define F0900_GPIO3_CONFIG 0xf143007e
#define F0900_GPIO3_XOR 0xf1430001

/*GPIO4CFG*/
#define R0900_GPIO4CFG 0xf144
#define F0900_GPIO4_OPD 0xf1440080
#define F0900_GPIO4_CONFIG 0xf144007e
#define F0900_GPIO4_XOR 0xf1440001

/*GPIO5CFG*/
#define R0900_GPIO5CFG 0xf145
#define F0900_GPIO5_OPD 0xf1450080
#define F0900_GPIO5_CONFIG 0xf145007e
#define F0900_GPIO5_XOR 0xf1450001

/*GPIO6CFG*/
#define R0900_GPIO6CFG 0xf146
#define F0900_GPIO6_OPD 0xf1460080
#define F0900_GPIO6_CONFIG 0xf146007e
#define F0900_GPIO6_XOR 0xf1460001

/*GPIO7CFG*/
#define R0900_GPIO7CFG 0xf147
#define F0900_GPIO7_OPD 0xf1470080
#define F0900_GPIO7_CONFIG 0xf147007e
#define F0900_GPIO7_XOR 0xf1470001

/*GPIO8CFG*/
#define R0900_GPIO8CFG 0xf148
#define F0900_GPIO8_OPD 0xf1480080
#define F0900_GPIO8_CONFIG 0xf148007e
#define F0900_GPIO8_XOR 0xf1480001

/*GPIO9CFG*/
#define R0900_GPIO9CFG 0xf149
#define F0900_GPIO9_OPD 0xf1490080
#define F0900_GPIO9_CONFIG 0xf149007e
#define F0900_GPIO9_XOR 0xf1490001

/*GPIO10CFG*/
#define R0900_GPIO10CFG 0xf14a
#define F0900_GPIO10_OPD 0xf14a0080
#define F0900_GPIO10_CONFIG 0xf14a007e
#define F0900_GPIO10_XOR 0xf14a0001

/*GPIO11CFG*/
#define R0900_GPIO11CFG 0xf14b
#define F0900_GPIO11_OPD 0xf14b0080
#define F0900_GPIO11_CONFIG 0xf14b007e
#define F0900_GPIO11_XOR 0xf14b0001

/*GPIO12CFG*/
#define R0900_GPIO12CFG 0xf14c
#define F0900_GPIO12_OPD 0xf14c0080
#define F0900_GPIO12_CONFIG 0xf14c007e
#define F0900_GPIO12_XOR 0xf14c0001

/*GPIO13CFG*/
#define R0900_GPIO13CFG 0xf14d
#define F0900_GPIO13_OPD 0xf14d0080
#define F0900_GPIO13_CONFIG 0xf14d007e
#define F0900_GPIO13_XOR 0xf14d0001

/*CS0CFG*/
#define R0900_CS0CFG 0xf14e
#define F0900_CS0_OPD 0xf14e0080
#define F0900_CS0_CONFIG 0xf14e007e
#define F0900_CS0_XOR 0xf14e0001

/*CS1CFG*/
#define R0900_CS1CFG 0xf14f
#define F0900_CS1_OPD 0xf14f0080
#define F0900_CS1_CONFIG 0xf14f007e
#define F0900_CS1_XOR 0xf14f0001

/*STDBYCFG*/
#define R0900_STDBYCFG 0xf150
#define F0900_STDBY_OPD 0xf1500080
#define F0900_STDBY_CONFIG 0xf150007e
#define F0900_STBDY_XOR 0xf1500001

/*DIRCLKCFG*/
#define R0900_DIRCLKCFG 0xf151
#define F0900_DIRCLK_OPD 0xf1510080
#define F0900_DIRCLK_CONFIG 0xf151007e
#define F0900_DIRCLK_XOR 0xf1510001

/*AGCRF1CFG*/
#define R0900_AGCRF1CFG 0xf152
#define F0900_AGCRF1_OPD 0xf1520080
#define F0900_AGCRF1_CONFIG 0xf152007e
#define F0900_AGCRF1_XOR 0xf1520001

/*SDAT1CFG*/
#define R0900_SDAT1CFG 0xf153
#define F0900_SDAT1_OPD 0xf1530080
#define F0900_SDAT1_CONFIG 0xf153007e
#define F0900_SDAT1_XOR 0xf1530001

/*SCLT1CFG*/
#define R0900_SCLT1CFG 0xf154
#define F0900_SCLT1_OPD 0xf1540080
#define F0900_SCLT1_CONFIG 0xf154007e
#define F0900_SCLT1_XOR 0xf1540001

/*DISEQCO1CFG*/
#define R0900_DISEQCO1CFG 0xf155
#define F0900_DISEQCO1_OPD 0xf1550080
#define F0900_DISEQCO1_CONFIG 0xf155007e
#define F0900_DISEQC1_XOR 0xf1550001

/*AGCRF2CFG*/
#define R0900_AGCRF2CFG 0xf156
#define F0900_AGCRF2_OPD 0xf1560080
#define F0900_AGCRF2_CONFIG 0xf156007e
#define F0900_AGCRF2_XOR 0xf1560001

/*SDAT2CFG*/
#define R0900_SDAT2CFG 0xf157
#define F0900_SDAT2_OPD 0xf1570080
#define F0900_SDAT2_CONFIG 0xf157007e
#define F0900_SDAT2_XOR 0xf1570001

/*SCLT2CFG*/
#define R0900_SCLT2CFG 0xf158
#define F0900_SCLT2_OPD 0xf1580080
#define F0900_SCLT2_CONFIG 0xf158007e
#define F0900_SCLT2_XOR 0xf1580001

/*DISEQCO2CFG*/
#define R0900_DISEQCO2CFG 0xf159
#define F0900_DISEQCO2_OPD 0xf1590080
#define F0900_DISEQCO2_CONFIG 0xf159007e
#define F0900_DISEQC2_XOR 0xf1590001

/*CLKOUT27CFG*/
#define R0900_CLKOUT27CFG 0xf15a
#define F0900_CLKOUT27_OPD 0xf15a0080
#define F0900_CLKOUT27_CONFIG 0xf15a007e
#define F0900_CLKOUT27_XOR 0xf15a0001

/*ERROR1CFG*/
#define R0900_ERROR1CFG 0xf15b
#define F0900_ERROR1_OPD 0xf15b0080
#define F0900_ERROR1_CONFIG 0xf15b007e
#define F0900_ERROR1_XOR 0xf15b0001

/*DPN1CFG*/
#define R0900_DPN1CFG 0xf15c
#define F0900_DPN1_OPD 0xf15c0080
#define F0900_DPN1_CONFIG 0xf15c007e
#define F0900_DPN1_XOR 0xf15c0001

/*STROUT1CFG*/
#define R0900_STROUT1CFG 0xf15d
#define F0900_STROUT1_OPD 0xf15d0080
#define F0900_STROUT1_CONFIG 0xf15d007e
#define F0900_STROUT1_XOR 0xf15d0001

/*CLKOUT1CFG*/
#define R0900_CLKOUT1CFG 0xf15e
#define F0900_CLKOUT1_OPD 0xf15e0080
#define F0900_CLKOUT1_CONFIG 0xf15e007e
#define F0900_CLKOUT1_XOR 0xf15e0001

/*DATA71CFG*/
#define R0900_DATA71CFG 0xf15f
#define F0900_DATA71_OPD 0xf15f0080
#define F0900_DATA71_CONFIG 0xf15f007e
#define F0900_DATA71_XOR 0xf15f0001

/*ERROR2CFG*/
#define R0900_ERROR2CFG 0xf160
#define F0900_ERROR2_OPD 0xf1600080
#define F0900_ERROR2_CONFIG 0xf160007e
#define F0900_ERROR2_XOR 0xf1600001

/*DPN2CFG*/
#define R0900_DPN2CFG 0xf161
#define F0900_DPN2_OPD 0xf1610080
#define F0900_DPN2_CONFIG 0xf161007e
#define F0900_DPN2_XOR 0xf1610001

/*STROUT2CFG*/
#define R0900_STROUT2CFG 0xf162
#define F0900_STROUT2_OPD 0xf1620080
#define F0900_STROUT2_CONFIG 0xf162007e
#define F0900_STROUT2_XOR 0xf1620001

/*CLKOUT2CFG*/
#define R0900_CLKOUT2CFG 0xf163
#define F0900_CLKOUT2_OPD 0xf1630080
#define F0900_CLKOUT2_CONFIG 0xf163007e
#define F0900_CLKOUT2_XOR 0xf1630001

/*DATA72CFG*/
#define R0900_DATA72CFG 0xf164
#define F0900_DATA72_OPD 0xf1640080
#define F0900_DATA72_CONFIG 0xf164007e
#define F0900_DATA72_XOR 0xf1640001

/*ERROR3CFG*/
#define R0900_ERROR3CFG 0xf165
#define F0900_ERROR3_OPD 0xf1650080
#define F0900_ERROR3_CONFIG 0xf165007e
#define F0900_ERROR3_XOR 0xf1650001

/*DPN3CFG*/
#define R0900_DPN3CFG 0xf166
#define F0900_DPN3_OPD 0xf1660080
#define F0900_DPN3_CONFIG 0xf166007e
#define F0900_DPN3_XOR 0xf1660001

/*STROUT3CFG*/
#define R0900_STROUT3CFG 0xf167
#define F0900_STROUT3_OPD 0xf1670080
#define F0900_STROUT3_CONFIG 0xf167007e
#define F0900_STROUT3_XOR 0xf1670001

/*CLKOUT3CFG*/
#define R0900_CLKOUT3CFG 0xf168
#define F0900_CLKOUT3_OPD 0xf1680080
#define F0900_CLKOUT3_CONFIG 0xf168007e
#define F0900_CLKOUT3_XOR 0xf1680001

/*DATA73CFG*/
#define R0900_DATA73CFG 0xf169
#define F0900_DATA73_OPD 0xf1690080
#define F0900_DATA73_CONFIG 0xf169007e
#define F0900_DATA73_XOR 0xf1690001

/*STRSTATUS1*/
#define R0900_STRSTATUS1 0xf16a
#define F0900_STRSTATUS_SEL2 0xf16a00f0
#define F0900_STRSTATUS_SEL1 0xf16a000f

/*STRSTATUS2*/
#define R0900_STRSTATUS2 0xf16b
#define F0900_STRSTATUS_SEL4 0xf16b00f0
#define F0900_STRSTATUS_SEL3 0xf16b000f

/*STRSTATUS3*/
#define R0900_STRSTATUS3 0xf16c
#define F0900_STRSTATUS_SEL6 0xf16c00f0
#define F0900_STRSTATUS_SEL5 0xf16c000f

/*FSKTFC2*/
#define R0900_FSKTFC2 0xf170
#define F0900_FSKT_KMOD 0xf17000fc
#define F0900_FSKT_CAR2 0xf1700003

/*FSKTFC1*/
#define R0900_FSKTFC1 0xf171
#define F0900_FSKT_CAR1 0xf17100ff

/*FSKTFC0*/
#define R0900_FSKTFC0 0xf172
#define F0900_FSKT_CAR0 0xf17200ff

/*FSKTDELTAF1*/
#define R0900_FSKTDELTAF1 0xf173
#define F0900_FSKT_DELTAF1 0xf173000f

/*FSKTDELTAF0*/
#define R0900_FSKTDELTAF0 0xf174
#define F0900_FSKT_DELTAF0 0xf17400ff

/*FSKTCTRL*/
#define R0900_FSKTCTRL 0xf175
#define F0900_FSKT_EN_SGN 0xf1750040
#define F0900_FSKT_MOD_SGN 0xf1750020
#define F0900_FSKT_MOD_EN 0xf175001c
#define F0900_FSKT_DACMODE 0xf1750003

/*FSKRFC2*/
#define R0900_FSKRFC2 0xf176
#define F0900_FSKR_DETSGN 0xf1760040
#define F0900_FSKR_OUTSGN 0xf1760020
#define F0900_FSKR_KAGC 0xf176001c
#define F0900_FSKR_CAR2 0xf1760003

/*FSKRFC1*/
#define R0900_FSKRFC1 0xf177
#define F0900_FSKR_CAR1 0xf17700ff

/*FSKRFC0*/
#define R0900_FSKRFC0 0xf178
#define F0900_FSKR_CAR0 0xf17800ff

/*FSKRK1*/
#define R0900_FSKRK1 0xf179
#define F0900_FSKR_K1_EXP 0xf17900e0
#define F0900_FSKR_K1_MANT 0xf179001f

/*FSKRK2*/
#define R0900_FSKRK2 0xf17a
#define F0900_FSKR_K2_EXP 0xf17a00e0
#define F0900_FSKR_K2_MANT 0xf17a001f

/*FSKRAGCR*/
#define R0900_FSKRAGCR 0xf17b
#define F0900_FSKR_OUTCTL 0xf17b00c0
#define F0900_FSKR_AGC_REF 0xf17b003f

/*FSKRAGC*/
#define R0900_FSKRAGC 0xf17c
#define F0900_FSKR_AGC_ACCU 0xf17c00ff

/*FSKRALPHA*/
#define R0900_FSKRALPHA 0xf17d
#define F0900_FSKR_ALPHA_EXP 0xf17d001c
#define F0900_FSKR_ALPHA_M 0xf17d0003

/*FSKRPLTH1*/
#define R0900_FSKRPLTH1 0xf17e
#define F0900_FSKR_BETA 0xf17e00f0
#define F0900_FSKR_PLL_TRESH1 0xf17e000f

/*FSKRPLTH0*/
#define R0900_FSKRPLTH0 0xf17f
#define F0900_FSKR_PLL_TRESH0 0xf17f00ff

/*FSKRDF1*/
#define R0900_FSKRDF1 0xf180
#define F0900_FSKR_OUT 0xf1800080
#define F0900_FSKR_DELTAF1 0xf180001f

/*FSKRDF0*/
#define R0900_FSKRDF0 0xf181
#define F0900_FSKR_DELTAF0 0xf18100ff

/*FSKRSTEPP*/
#define R0900_FSKRSTEPP 0xf182
#define F0900_FSKR_STEP_PLUS 0xf18200ff

/*FSKRSTEPM*/
#define R0900_FSKRSTEPM 0xf183
#define F0900_FSKR_STEP_MINUS 0xf18300ff

/*FSKRDET1*/
#define R0900_FSKRDET1 0xf184
#define F0900_FSKR_DETECT 0xf1840080
#define F0900_FSKR_CARDET_ACCU1 0xf184000f

/*FSKRDET0*/
#define R0900_FSKRDET0 0xf185
#define F0900_FSKR_CARDET_ACCU0 0xf18500ff

/*FSKRDTH1*/
#define R0900_FSKRDTH1 0xf186
#define F0900_FSKR_CARLOSS_THRESH1 0xf18600f0
#define F0900_FSKR_CARDET_THRESH1 0xf186000f

/*FSKRDTH0*/
#define R0900_FSKRDTH0 0xf187
#define F0900_FSKR_CARDET_THRESH0 0xf18700ff

/*FSKRLOSS*/
#define R0900_FSKRLOSS 0xf188
#define F0900_FSKR_CARLOSS_THRESH0 0xf18800ff

/*P2_DISTXCTL*/
#define R0900_P2_DISTXCTL 0xf190
#define F0900_P2_TIM_OFF 0xf1900080
#define F0900_P2_DISEQC_RESET 0xf1900040
#define F0900_P2_TIM_CMD 0xf1900030
#define F0900_P2_DIS_PRECHARGE 0xf1900008
#define F0900_P2_DISTX_MODE 0xf1900007

/*P2_DISRXCTL*/
#define R0900_P2_DISRXCTL 0xf191
#define F0900_P2_RECEIVER_ON 0xf1910080
#define F0900_P2_IGNO_SHORT22K 0xf1910040
#define F0900_P2_ONECHIP_TRX 0xf1910020
#define F0900_P2_EXT_ENVELOP 0xf1910010
#define F0900_P2_PIN_SELECT0 0xf191000c
#define F0900_P2_IRQ_RXEND 0xf1910002
#define F0900_P2_IRQ_4NBYTES 0xf1910001

/*P2_DISRX_ST0*/
#define R0900_P2_DISRX_ST0 0xf194
#define F0900_P2_RX_END 0xf1940080
#define F0900_P2_RX_ACTIVE 0xf1940040
#define F0900_P2_SHORT_22KHZ 0xf1940020
#define F0900_P2_CONT_TONE 0xf1940010
#define F0900_P2_FIFO_4BREADY 0xf1940008
#define F0900_P2_FIFO_EMPTY 0xf1940004
#define F0900_P2_ABORT_DISRX 0xf1940001

/*P2_DISRX_ST1*/
#define R0900_P2_DISRX_ST1 0xf195
#define F0900_P2_RX_FAIL 0xf1950080
#define F0900_P2_FIFO_PARITYFAIL 0xf1950040
#define F0900_P2_RX_NONBYTE 0xf1950020
#define F0900_P2_FIFO_OVERFLOW 0xf1950010
#define F0900_P2_FIFO_BYTENBR 0xf195000f

/*P2_DISRXDATA*/
#define R0900_P2_DISRXDATA 0xf196
#define F0900_P2_DISRX_DATA 0xf19600ff

/*P2_DISTXDATA*/
#define R0900_P2_DISTXDATA 0xf197
#define F0900_P2_DISEQC_FIFO 0xf19700ff

/*P2_DISTXSTATUS*/
#define R0900_P2_DISTXSTATUS 0xf198
#define F0900_P2_TX_FAIL 0xf1980080
#define F0900_P2_FIFO_FULL 0xf1980040
#define F0900_P2_TX_IDLE 0xf1980020
#define F0900_P2_GAP_BURST 0xf1980010
#define F0900_P2_TXFIFO_BYTES 0xf198000f

/*P2_F22TX*/
#define R0900_P2_F22TX 0xf199
#define F0900_P2_F22_REG 0xf19900ff

/*P2_F22RX*/
#define R0900_P2_F22RX 0xf19a
#define F0900_P2_F22RX_REG 0xf19a00ff

/*P2_ACRPRESC*/
#define R0900_P2_ACRPRESC 0xf19c
#define F0900_P2_ACR_PRESC 0xf19c0007

/*P2_ACRDIV*/
#define R0900_P2_ACRDIV 0xf19d
#define F0900_P2_ACR_DIV 0xf19d00ff

/*P1_DISTXCTL*/
#define R0900_P1_DISTXCTL 0xf1a0
#define DISTXCTL shiftx(R0900_P1_DISTXCTL, demod, 0x10)
#define F0900_P1_TIM_OFF 0xf1a00080
#define F0900_P1_DISEQC_RESET 0xf1a00040
#define DISEQC_RESET shiftx(F0900_P1_DISEQC_RESET, demod, 0x100000)
#define F0900_P1_TIM_CMD 0xf1a00030
#define F0900_P1_DIS_PRECHARGE 0xf1a00008
#define DIS_PRECHARGE shiftx(F0900_P1_DIS_PRECHARGE, demod, 0x100000)
#define F0900_P1_DISTX_MODE 0xf1a00007
#define DISTX_MODE shiftx(F0900_P1_DISTX_MODE, demod, 0x100000)

/*P1_DISRXCTL*/
#define R0900_P1_DISRXCTL 0xf1a1
#define DISRXCTL shiftx(R0900_P1_DISRXCTL, demod, 0x10)
#define F0900_P1_RECEIVER_ON 0xf1a10080
#define F0900_P1_IGNO_SHORT22K 0xf1a10040
#define F0900_P1_ONECHIP_TRX 0xf1a10020
#define F0900_P1_EXT_ENVELOP 0xf1a10010
#define F0900_P1_PIN_SELECT0 0xf1a1000c
#define F0900_P1_IRQ_RXEND 0xf1a10002
#define F0900_P1_IRQ_4NBYTES 0xf1a10001

/*P1_DISRX_ST0*/
#define R0900_P1_DISRX_ST0 0xf1a4
#define DISRX_ST0 shiftx(R0900_P1_DISRX_ST0, demod, 0x10)
#define F0900_P1_RX_END 0xf1a40080
#define RX_END shiftx(F0900_P1_RX_END, demod, 0x100000)
#define F0900_P1_RX_ACTIVE 0xf1a40040
#define F0900_P1_SHORT_22KHZ 0xf1a40020
#define F0900_P1_CONT_TONE 0xf1a40010
#define F0900_P1_FIFO_4BREADY 0xf1a40008
#define F0900_P1_FIFO_EMPTY 0xf1a40004
#define F0900_P1_ABORT_DISRX 0xf1a40001

/*P1_DISRX_ST1*/
#define R0900_P1_DISRX_ST1 0xf1a5
#define DISRX_ST1 shiftx(R0900_P1_DISRX_ST1, demod, 0x10)
#define F0900_P1_RX_FAIL 0xf1a50080
#define F0900_P1_FIFO_PARITYFAIL 0xf1a50040
#define F0900_P1_RX_NONBYTE 0xf1a50020
#define F0900_P1_FIFO_OVERFLOW 0xf1a50010
#define F0900_P1_FIFO_BYTENBR 0xf1a5000f
#define FIFO_BYTENBR shiftx(F0900_P1_FIFO_BYTENBR, demod, 0x100000)

/*P1_DISRXDATA*/
#define R0900_P1_DISRXDATA 0xf1a6
#define DISRXDATA shiftx(R0900_P1_DISRXDATA, demod, 0x10)
#define F0900_P1_DISRX_DATA 0xf1a600ff

/*P1_DISTXDATA*/
#define R0900_P1_DISTXDATA 0xf1a7
#define DISTXDATA shiftx(R0900_P1_DISTXDATA, demod, 0x10)
#define F0900_P1_DISEQC_FIFO 0xf1a700ff

/*P1_DISTXSTATUS*/
#define R0900_P1_DISTXSTATUS 0xf1a8
#define F0900_P1_TX_FAIL 0xf1a80080
#define F0900_P1_FIFO_FULL 0xf1a80040
#define FIFO_FULL shiftx(F0900_P1_FIFO_FULL, demod, 0x100000)
#define F0900_P1_TX_IDLE 0xf1a80020
#define TX_IDLE shiftx(F0900_P1_TX_IDLE, demod, 0x100000)
#define F0900_P1_GAP_BURST 0xf1a80010
#define F0900_P1_TXFIFO_BYTES 0xf1a8000f

/*P1_F22TX*/
#define R0900_P1_F22TX 0xf1a9
#define F22TX shiftx(R0900_P1_F22TX, demod, 0x10)
#define F0900_P1_F22_REG 0xf1a900ff

/*P1_F22RX*/
#define R0900_P1_F22RX 0xf1aa
#define F22RX shiftx(R0900_P1_F22RX, demod, 0x10)
#define F0900_P1_F22RX_REG 0xf1aa00ff

/*P1_ACRPRESC*/
#define R0900_P1_ACRPRESC 0xf1ac
#define ACRPRESC shiftx(R0900_P1_ACRPRESC, demod, 0x10)
#define F0900_P1_ACR_PRESC 0xf1ac0007

/*P1_ACRDIV*/
#define R0900_P1_ACRDIV 0xf1ad
#define ACRDIV shiftx(R0900_P1_ACRDIV, demod, 0x10)
#define F0900_P1_ACR_DIV 0xf1ad00ff

/*NCOARSE*/
#define R0900_NCOARSE 0xf1b3
#define F0900_M_DIV 0xf1b300ff

/*SYNTCTRL*/
#define R0900_SYNTCTRL 0xf1b6
#define F0900_STANDBY 0xf1b60080
#define F0900_BYPASSPLLCORE 0xf1b60040
#define F0900_SELX1RATIO 0xf1b60020
#define F0900_STOP_PLL 0xf1b60008
#define F0900_BYPASSPLLFSK 0xf1b60004
#define F0900_SELOSCI 0xf1b60002
#define F0900_BYPASSPLLADC 0xf1b60001

/*FILTCTRL*/
#define R0900_FILTCTRL 0xf1b7
#define F0900_INV_CLK135 0xf1b70080
#define F0900_SEL_FSKCKDIV 0xf1b70004
#define F0900_INV_CLKFSK 0xf1b70002
#define F0900_BYPASS_APPLI 0xf1b70001

/*PLLSTAT*/
#define R0900_PLLSTAT 0xf1b8
#define F0900_PLLLOCK 0xf1b80001

/*STOPCLK1*/
#define R0900_STOPCLK1 0xf1c2
#define F0900_STOP_CLKPKDT2 0xf1c20040
#define F0900_STOP_CLKPKDT1 0xf1c20020
#define F0900_STOP_CLKFEC 0xf1c20010
#define F0900_STOP_CLKADCI2 0xf1c20008
#define F0900_INV_CLKADCI2 0xf1c20004
#define F0900_STOP_CLKADCI1 0xf1c20002
#define F0900_INV_CLKADCI1 0xf1c20001

/*STOPCLK2*/
#define R0900_STOPCLK2 0xf1c3
#define F0900_STOP_CLKSAMP2 0xf1c30010
#define F0900_STOP_CLKSAMP1 0xf1c30008
#define F0900_STOP_CLKVIT2 0xf1c30004
#define F0900_STOP_CLKVIT1 0xf1c30002
#define STOP_CLKVIT shiftx(F0900_STOP_CLKVIT1, demod, -2)
#define F0900_STOP_CLKTS 0xf1c30001

/*TSTTNR0*/
#define R0900_TSTTNR0 0xf1df
#define F0900_SEL_FSK 0xf1df0080
#define F0900_FSK_PON 0xf1df0004

/*TSTTNR1*/
#define R0900_TSTTNR1 0xf1e0
#define F0900_ADC1_PON 0xf1e00002
#define F0900_ADC1_INMODE 0xf1e00001

/*TSTTNR2*/
#define R0900_TSTTNR2 0xf1e1
#define F0900_DISEQC1_PON 0xf1e10020

/*TSTTNR3*/
#define R0900_TSTTNR3 0xf1e2
#define F0900_ADC2_PON 0xf1e20002
#define F0900_ADC2_INMODE 0xf1e20001

/*TSTTNR4*/
#define R0900_TSTTNR4 0xf1e3
#define F0900_DISEQC2_PON 0xf1e30020

/*P2_IQCONST*/
#define R0900_P2_IQCONST 0xf200
#define F0900_P2_CONSTEL_SELECT 0xf2000060
#define F0900_P2_IQSYMB_SEL 0xf200001f

/*P2_NOSCFG*/
#define R0900_P2_NOSCFG 0xf201
#define F0900_P2_DUMMYPL_NOSDATA 0xf2010020
#define F0900_P2_NOSPLH_BETA 0xf2010018
#define F0900_P2_NOSDATA_BETA 0xf2010007

/*P2_ISYMB*/
#define R0900_P2_ISYMB 0xf202
#define F0900_P2_I_SYMBOL 0xf20201ff

/*P2_QSYMB*/
#define R0900_P2_QSYMB 0xf203
#define F0900_P2_Q_SYMBOL 0xf20301ff

/*P2_AGC1CFG*/
#define R0900_P2_AGC1CFG 0xf204
#define F0900_P2_DC_FROZEN 0xf2040080
#define F0900_P2_DC_CORRECT 0xf2040040
#define F0900_P2_AMM_FROZEN 0xf2040020
#define F0900_P2_AMM_CORRECT 0xf2040010
#define F0900_P2_QUAD_FROZEN 0xf2040008
#define F0900_P2_QUAD_CORRECT 0xf2040004

/*P2_AGC1CN*/
#define R0900_P2_AGC1CN 0xf206
#define F0900_P2_AGC1_LOCKED 0xf2060080
#define F0900_P2_AGC1_MINPOWER 0xf2060010
#define F0900_P2_AGCOUT_FAST 0xf2060008
#define F0900_P2_AGCIQ_BETA 0xf2060007

/*P2_AGC1REF*/
#define R0900_P2_AGC1REF 0xf207
#define F0900_P2_AGCIQ_REF 0xf20700ff

/*P2_IDCCOMP*/
#define R0900_P2_IDCCOMP 0xf208
#define F0900_P2_IAVERAGE_ADJ 0xf20801ff

/*P2_QDCCOMP*/
#define R0900_P2_QDCCOMP 0xf209
#define F0900_P2_QAVERAGE_ADJ 0xf20901ff

/*P2_POWERI*/
#define R0900_P2_POWERI 0xf20a
#define F0900_P2_POWER_I 0xf20a00ff

/*P2_POWERQ*/
#define R0900_P2_POWERQ 0xf20b
#define F0900_P2_POWER_Q 0xf20b00ff

/*P2_AGC1AMM*/
#define R0900_P2_AGC1AMM 0xf20c
#define F0900_P2_AMM_VALUE 0xf20c00ff

/*P2_AGC1QUAD*/
#define R0900_P2_AGC1QUAD 0xf20d
#define F0900_P2_QUAD_VALUE 0xf20d01ff

/*P2_AGCIQIN1*/
#define R0900_P2_AGCIQIN1 0xf20e
#define F0900_P2_AGCIQ_VALUE1 0xf20e00ff

/*P2_AGCIQIN0*/
#define R0900_P2_AGCIQIN0 0xf20f
#define F0900_P2_AGCIQ_VALUE0 0xf20f00ff

/*P2_DEMOD*/
#define R0900_P2_DEMOD 0xf210
#define F0900_P2_MANUALS2_ROLLOFF 0xf2100080
#define F0900_P2_SPECINV_CONTROL 0xf2100030
#define F0900_P2_FORCE_ENASAMP 0xf2100008
#define F0900_P2_MANUALSX_ROLLOFF 0xf2100004
#define F0900_P2_ROLLOFF_CONTROL 0xf2100003

/*P2_DMDMODCOD*/
#define R0900_P2_DMDMODCOD 0xf211
#define F0900_P2_MANUAL_MODCOD 0xf2110080
#define F0900_P2_DEMOD_MODCOD 0xf211007c
#define F0900_P2_DEMOD_TYPE 0xf2110003

/*P2_DSTATUS*/
#define R0900_P2_DSTATUS 0xf212
#define F0900_P2_CAR_LOCK 0xf2120080
#define F0900_P2_TMGLOCK_QUALITY 0xf2120060
#define F0900_P2_LOCK_DEFINITIF 0xf2120008
#define F0900_P2_OVADC_DETECT 0xf2120001

/*P2_DSTATUS2*/
#define R0900_P2_DSTATUS2 0xf213
#define F0900_P2_DEMOD_DELOCK 0xf2130080
#define F0900_P2_AGC1_NOSIGNALACK 0xf2130008
#define F0900_P2_AGC2_OVERFLOW 0xf2130004
#define F0900_P2_CFR_OVERFLOW 0xf2130002
#define F0900_P2_GAMMA_OVERUNDER 0xf2130001

/*P2_DMDCFGMD*/
#define R0900_P2_DMDCFGMD 0xf214
#define F0900_P2_DVBS2_ENABLE 0xf2140080
#define F0900_P2_DVBS1_ENABLE 0xf2140040
#define F0900_P2_SCAN_ENABLE 0xf2140010
#define F0900_P2_CFR_AUTOSCAN 0xf2140008
#define F0900_P2_TUN_RNG 0xf2140003

/*P2_DMDCFG2*/
#define R0900_P2_DMDCFG2 0xf215
#define F0900_P2_S1S2_SEQUENTIAL 0xf2150040
#define F0900_P2_INFINITE_RELOCK 0xf2150010

/*P2_DMDISTATE*/
#define R0900_P2_DMDISTATE 0xf216
#define F0900_P2_I2C_DEMOD_MODE 0xf216001f

/*P2_DMDT0M*/
#define R0900_P2_DMDT0M 0xf217
#define F0900_P2_DMDT0_MIN 0xf21700ff

/*P2_DMDSTATE*/
#define R0900_P2_DMDSTATE 0xf21b
#define F0900_P2_HEADER_MODE 0xf21b0060

/*P2_DMDFLYW*/
#define R0900_P2_DMDFLYW 0xf21c
#define F0900_P2_I2C_IRQVAL 0xf21c00f0
#define F0900_P2_FLYWHEEL_CPT 0xf21c000f

/*P2_DSTATUS3*/
#define R0900_P2_DSTATUS3 0xf21d
#define F0900_P2_DEMOD_CFGMODE 0xf21d0060

/*P2_DMDCFG3*/
#define R0900_P2_DMDCFG3 0xf21e
#define F0900_P2_NOSTOP_FIFOFULL 0xf21e0008

/*P2_DMDCFG4*/
#define R0900_P2_DMDCFG4 0xf21f
#define F0900_P2_TUNER_NRELAUNCH 0xf21f0008

/*P2_CORRELMANT*/
#define R0900_P2_CORRELMANT 0xf220
#define F0900_P2_CORREL_MANT 0xf22000ff

/*P2_CORRELABS*/
#define R0900_P2_CORRELABS 0xf221
#define F0900_P2_CORREL_ABS 0xf22100ff

/*P2_CORRELEXP*/
#define R0900_P2_CORRELEXP 0xf222
#define F0900_P2_CORREL_ABSEXP 0xf22200f0
#define F0900_P2_CORREL_EXP 0xf222000f

/*P2_PLHMODCOD*/
#define R0900_P2_PLHMODCOD 0xf224
#define F0900_P2_SPECINV_DEMOD 0xf2240080
#define F0900_P2_PLH_MODCOD 0xf224007c
#define F0900_P2_PLH_TYPE 0xf2240003

/*P2_DMDREG*/
#define R0900_P2_DMDREG 0xf225
#define F0900_P2_DECIM_PLFRAMES 0xf2250001

/*P2_AGC2O*/
#define R0900_P2_AGC2O 0xf22c
#define F0900_P2_AGC2_COEF 0xf22c0007

/*P2_AGC2REF*/
#define R0900_P2_AGC2REF 0xf22d
#define F0900_P2_AGC2_REF 0xf22d00ff

/*P2_AGC1ADJ*/
#define R0900_P2_AGC1ADJ 0xf22e
#define F0900_P2_AGC1_ADJUSTED 0xf22e007f

/*P2_AGC2I1*/
#define R0900_P2_AGC2I1 0xf236
#define F0900_P2_AGC2_INTEGRATOR1 0xf23600ff

/*P2_AGC2I0*/
#define R0900_P2_AGC2I0 0xf237
#define F0900_P2_AGC2_INTEGRATOR0 0xf23700ff

/*P2_CARCFG*/
#define R0900_P2_CARCFG 0xf238
#define F0900_P2_CFRUPLOW_AUTO 0xf2380080
#define F0900_P2_CFRUPLOW_TEST 0xf2380040
#define F0900_P2_ROTAON 0xf2380004
#define F0900_P2_PH_DET_ALGO 0xf2380003

/*P2_ACLC*/
#define R0900_P2_ACLC 0xf239
#define F0900_P2_CAR_ALPHA_MANT 0xf2390030
#define F0900_P2_CAR_ALPHA_EXP 0xf239000f

/*P2_BCLC*/
#define R0900_P2_BCLC 0xf23a
#define F0900_P2_CAR_BETA_MANT 0xf23a0030
#define F0900_P2_CAR_BETA_EXP 0xf23a000f

/*P2_CARFREQ*/
#define R0900_P2_CARFREQ 0xf23d
#define F0900_P2_KC_COARSE_EXP 0xf23d00f0
#define F0900_P2_BETA_FREQ 0xf23d000f

/*P2_CARHDR*/
#define R0900_P2_CARHDR 0xf23e
#define F0900_P2_K_FREQ_HDR 0xf23e00ff

/*P2_LDT*/
#define R0900_P2_LDT 0xf23f
#define F0900_P2_CARLOCK_THRES 0xf23f01ff

/*P2_LDT2*/
#define R0900_P2_LDT2 0xf240
#define F0900_P2_CARLOCK_THRES2 0xf24001ff

/*P2_CFRICFG*/
#define R0900_P2_CFRICFG 0xf241
#define F0900_P2_NEG_CFRSTEP 0xf2410001

/*P2_CFRUP1*/
#define R0900_P2_CFRUP1 0xf242
#define F0900_P2_CFR_UP1 0xf24201ff

/*P2_CFRUP0*/
#define R0900_P2_CFRUP0 0xf243
#define F0900_P2_CFR_UP0 0xf24300ff

/*P2_CFRLOW1*/
#define R0900_P2_CFRLOW1 0xf246
#define F0900_P2_CFR_LOW1 0xf24601ff

/*P2_CFRLOW0*/
#define R0900_P2_CFRLOW0 0xf247
#define F0900_P2_CFR_LOW0 0xf24700ff

/*P2_CFRINIT1*/
#define R0900_P2_CFRINIT1 0xf248
#define F0900_P2_CFR_INIT1 0xf24801ff

/*P2_CFRINIT0*/
#define R0900_P2_CFRINIT0 0xf249
#define F0900_P2_CFR_INIT0 0xf24900ff

/*P2_CFRINC1*/
#define R0900_P2_CFRINC1 0xf24a
#define F0900_P2_MANUAL_CFRINC 0xf24a0080
#define F0900_P2_CFR_INC1 0xf24a003f

/*P2_CFRINC0*/
#define R0900_P2_CFRINC0 0xf24b
#define F0900_P2_CFR_INC0 0xf24b00f8

/*P2_CFR2*/
#define R0900_P2_CFR2 0xf24c
#define F0900_P2_CAR_FREQ2 0xf24c01ff

/*P2_CFR1*/
#define R0900_P2_CFR1 0xf24d
#define F0900_P2_CAR_FREQ1 0xf24d00ff

/*P2_CFR0*/
#define R0900_P2_CFR0 0xf24e
#define F0900_P2_CAR_FREQ0 0xf24e00ff

/*P2_LDI*/
#define R0900_P2_LDI 0xf24f
#define F0900_P2_LOCK_DET_INTEGR 0xf24f01ff

/*P2_TMGCFG*/
#define R0900_P2_TMGCFG 0xf250
#define F0900_P2_TMGLOCK_BETA 0xf25000c0
#define F0900_P2_DO_TIMING_CORR 0xf2500010
#define F0900_P2_TMG_MINFREQ 0xf2500003

/*P2_RTC*/
#define R0900_P2_RTC 0xf251
#define F0900_P2_TMGALPHA_EXP 0xf25100f0
#define F0900_P2_TMGBETA_EXP 0xf251000f

/*P2_RTCS2*/
#define R0900_P2_RTCS2 0xf252
#define F0900_P2_TMGALPHAS2_EXP 0xf25200f0
#define F0900_P2_TMGBETAS2_EXP 0xf252000f

/*P2_TMGTHRISE*/
#define R0900_P2_TMGTHRISE 0xf253
#define F0900_P2_TMGLOCK_THRISE 0xf25300ff

/*P2_TMGTHFALL*/
#define R0900_P2_TMGTHFALL 0xf254
#define F0900_P2_TMGLOCK_THFALL 0xf25400ff

/*P2_SFRUPRATIO*/
#define R0900_P2_SFRUPRATIO 0xf255
#define F0900_P2_SFR_UPRATIO 0xf25500ff

/*P2_SFRLOWRATIO*/
#define R0900_P2_SFRLOWRATIO 0xf256
#define F0900_P2_SFR_LOWRATIO 0xf25600ff

/*P2_KREFTMG*/
#define R0900_P2_KREFTMG 0xf258
#define F0900_P2_KREF_TMG 0xf25800ff

/*P2_SFRSTEP*/
#define R0900_P2_SFRSTEP 0xf259
#define F0900_P2_SFR_SCANSTEP 0xf25900f0
#define F0900_P2_SFR_CENTERSTEP 0xf259000f

/*P2_TMGCFG2*/
#define R0900_P2_TMGCFG2 0xf25a
#define F0900_P2_SFRRATIO_FINE 0xf25a0001

/*P2_KREFTMG2*/
#define R0900_P2_KREFTMG2 0xf25b
#define F0900_P2_KREF_TMG2 0xf25b00ff

/*P2_SFRINIT1*/
#define R0900_P2_SFRINIT1 0xf25e
#define F0900_P2_SFR_INIT1 0xf25e007f

/*P2_SFRINIT0*/
#define R0900_P2_SFRINIT0 0xf25f
#define F0900_P2_SFR_INIT0 0xf25f00ff

/*P2_SFRUP1*/
#define R0900_P2_SFRUP1 0xf260
#define F0900_P2_AUTO_GUP 0xf2600080
#define F0900_P2_SYMB_FREQ_UP1 0xf260007f

/*P2_SFRUP0*/
#define R0900_P2_SFRUP0 0xf261
#define F0900_P2_SYMB_FREQ_UP0 0xf26100ff

/*P2_SFRLOW1*/
#define R0900_P2_SFRLOW1 0xf262
#define F0900_P2_AUTO_GLOW 0xf2620080
#define F0900_P2_SYMB_FREQ_LOW1 0xf262007f

/*P2_SFRLOW0*/
#define R0900_P2_SFRLOW0 0xf263
#define F0900_P2_SYMB_FREQ_LOW0 0xf26300ff

/*P2_SFR3*/
#define R0900_P2_SFR3 0xf264
#define F0900_P2_SYMB_FREQ3 0xf26400ff

/*P2_SFR2*/
#define R0900_P2_SFR2 0xf265
#define F0900_P2_SYMB_FREQ2 0xf26500ff

/*P2_SFR1*/
#define R0900_P2_SFR1 0xf266
#define F0900_P2_SYMB_FREQ1 0xf26600ff

/*P2_SFR0*/
#define R0900_P2_SFR0 0xf267
#define F0900_P2_SYMB_FREQ0 0xf26700ff

/*P2_TMGREG2*/
#define R0900_P2_TMGREG2 0xf268
#define F0900_P2_TMGREG2 0xf26800ff

/*P2_TMGREG1*/
#define R0900_P2_TMGREG1 0xf269
#define F0900_P2_TMGREG1 0xf26900ff

/*P2_TMGREG0*/
#define R0900_P2_TMGREG0 0xf26a
#define F0900_P2_TMGREG0 0xf26a00ff

/*P2_TMGLOCK1*/
#define R0900_P2_TMGLOCK1 0xf26b
#define F0900_P2_TMGLOCK_LEVEL1 0xf26b01ff

/*P2_TMGLOCK0*/
#define R0900_P2_TMGLOCK0 0xf26c
#define F0900_P2_TMGLOCK_LEVEL0 0xf26c00ff

/*P2_TMGOBS*/
#define R0900_P2_TMGOBS 0xf26d
#define F0900_P2_ROLLOFF_STATUS 0xf26d00c0

/*P2_EQUALCFG*/
#define R0900_P2_EQUALCFG 0xf26f
#define F0900_P2_EQUAL_ON 0xf26f0040
#define F0900_P2_MU_EQUALDFE 0xf26f0007

/*P2_EQUAI1*/
#define R0900_P2_EQUAI1 0xf270
#define F0900_P2_EQUA_ACCI1 0xf27001ff

/*P2_EQUAQ1*/
#define R0900_P2_EQUAQ1 0xf271
#define F0900_P2_EQUA_ACCQ1 0xf27101ff

/*P2_EQUAI2*/
#define R0900_P2_EQUAI2 0xf272
#define F0900_P2_EQUA_ACCI2 0xf27201ff

/*P2_EQUAQ2*/
#define R0900_P2_EQUAQ2 0xf273
#define F0900_P2_EQUA_ACCQ2 0xf27301ff

/*P2_EQUAI3*/
#define R0900_P2_EQUAI3 0xf274
#define F0900_P2_EQUA_ACCI3 0xf27401ff

/*P2_EQUAQ3*/
#define R0900_P2_EQUAQ3 0xf275
#define F0900_P2_EQUA_ACCQ3 0xf27501ff

/*P2_EQUAI4*/
#define R0900_P2_EQUAI4 0xf276
#define F0900_P2_EQUA_ACCI4 0xf27601ff

/*P2_EQUAQ4*/
#define R0900_P2_EQUAQ4 0xf277
#define F0900_P2_EQUA_ACCQ4 0xf27701ff

/*P2_EQUAI5*/
#define R0900_P2_EQUAI5 0xf278
#define F0900_P2_EQUA_ACCI5 0xf27801ff

/*P2_EQUAQ5*/
#define R0900_P2_EQUAQ5 0xf279
#define F0900_P2_EQUA_ACCQ5 0xf27901ff

/*P2_EQUAI6*/
#define R0900_P2_EQUAI6 0xf27a
#define F0900_P2_EQUA_ACCI6 0xf27a01ff

/*P2_EQUAQ6*/
#define R0900_P2_EQUAQ6 0xf27b
#define F0900_P2_EQUA_ACCQ6 0xf27b01ff

/*P2_EQUAI7*/
#define R0900_P2_EQUAI7 0xf27c
#define F0900_P2_EQUA_ACCI7 0xf27c01ff

/*P2_EQUAQ7*/
#define R0900_P2_EQUAQ7 0xf27d
#define F0900_P2_EQUA_ACCQ7 0xf27d01ff

/*P2_EQUAI8*/
#define R0900_P2_EQUAI8 0xf27e
#define F0900_P2_EQUA_ACCI8 0xf27e01ff

/*P2_EQUAQ8*/
#define R0900_P2_EQUAQ8 0xf27f
#define F0900_P2_EQUA_ACCQ8 0xf27f01ff

/*P2_NNOSDATAT1*/
#define R0900_P2_NNOSDATAT1 0xf280
#define F0900_P2_NOSDATAT_NORMED1 0xf28000ff

/*P2_NNOSDATAT0*/
#define R0900_P2_NNOSDATAT0 0xf281
#define F0900_P2_NOSDATAT_NORMED0 0xf28100ff

/*P2_NNOSDATA1*/
#define R0900_P2_NNOSDATA1 0xf282
#define F0900_P2_NOSDATA_NORMED1 0xf28200ff

/*P2_NNOSDATA0*/
#define R0900_P2_NNOSDATA0 0xf283
#define F0900_P2_NOSDATA_NORMED0 0xf28300ff

/*P2_NNOSPLHT1*/
#define R0900_P2_NNOSPLHT1 0xf284
#define F0900_P2_NOSPLHT_NORMED1 0xf28400ff

/*P2_NNOSPLHT0*/
#define R0900_P2_NNOSPLHT0 0xf285
#define F0900_P2_NOSPLHT_NORMED0 0xf28500ff

/*P2_NNOSPLH1*/
#define R0900_P2_NNOSPLH1 0xf286
#define F0900_P2_NOSPLH_NORMED1 0xf28600ff

/*P2_NNOSPLH0*/
#define R0900_P2_NNOSPLH0 0xf287
#define F0900_P2_NOSPLH_NORMED0 0xf28700ff

/*P2_NOSDATAT1*/
#define R0900_P2_NOSDATAT1 0xf288
#define F0900_P2_NOSDATAT_UNNORMED1 0xf28800ff

/*P2_NOSDATAT0*/
#define R0900_P2_NOSDATAT0 0xf289
#define F0900_P2_NOSDATAT_UNNORMED0 0xf28900ff

/*P2_NOSDATA1*/
#define R0900_P2_NOSDATA1 0xf28a
#define F0900_P2_NOSDATA_UNNORMED1 0xf28a00ff

/*P2_NOSDATA0*/
#define R0900_P2_NOSDATA0 0xf28b
#define F0900_P2_NOSDATA_UNNORMED0 0xf28b00ff

/*P2_NOSPLHT1*/
#define R0900_P2_NOSPLHT1 0xf28c
#define F0900_P2_NOSPLHT_UNNORMED1 0xf28c00ff

/*P2_NOSPLHT0*/
#define R0900_P2_NOSPLHT0 0xf28d
#define F0900_P2_NOSPLHT_UNNORMED0 0xf28d00ff

/*P2_NOSPLH1*/
#define R0900_P2_NOSPLH1 0xf28e
#define F0900_P2_NOSPLH_UNNORMED1 0xf28e00ff

/*P2_NOSPLH0*/
#define R0900_P2_NOSPLH0 0xf28f
#define F0900_P2_NOSPLH_UNNORMED0 0xf28f00ff

/*P2_CAR2CFG*/
#define R0900_P2_CAR2CFG 0xf290
#define F0900_P2_CARRIER3_DISABLE 0xf2900040
#define F0900_P2_ROTA2ON 0xf2900004
#define F0900_P2_PH_DET_ALGO2 0xf2900003

/*P2_CFR2CFR1*/
#define R0900_P2_CFR2CFR1 0xf291
#define F0900_P2_CFR2TOCFR1_DVBS1 0xf29100c0
#define F0900_P2_EN_S2CAR2CENTER 0xf2910020
#define F0900_P2_DIS_BCHERRCFR2 0xf2910010
#define F0900_P2_CFR2TOCFR1_BETA 0xf2910007

/*P2_CFR22*/
#define R0900_P2_CFR22 0xf293
#define F0900_P2_CAR2_FREQ2 0xf29301ff

/*P2_CFR21*/
#define R0900_P2_CFR21 0xf294
#define F0900_P2_CAR2_FREQ1 0xf29400ff

/*P2_CFR20*/
#define R0900_P2_CFR20 0xf295
#define F0900_P2_CAR2_FREQ0 0xf29500ff

/*P2_ACLC2S2Q*/
#define R0900_P2_ACLC2S2Q 0xf297
#define F0900_P2_ENAB_SPSKSYMB 0xf2970080
#define F0900_P2_CAR2S2_Q_ALPH_M 0xf2970030
#define F0900_P2_CAR2S2_Q_ALPH_E 0xf297000f

/*P2_ACLC2S28*/
#define R0900_P2_ACLC2S28 0xf298
#define F0900_P2_OLDI3Q_MODE 0xf2980080
#define F0900_P2_CAR2S2_8_ALPH_M 0xf2980030
#define F0900_P2_CAR2S2_8_ALPH_E 0xf298000f

/*P2_ACLC2S216A*/
#define R0900_P2_ACLC2S216A 0xf299
#define F0900_P2_DIS_C3STOPA2 0xf2990080
#define F0900_P2_CAR2S2_16ADERAT 0xf2990040
#define F0900_P2_CAR2S2_16A_ALPH_M 0xf2990030
#define F0900_P2_CAR2S2_16A_ALPH_E 0xf299000f

/*P2_ACLC2S232A*/
#define R0900_P2_ACLC2S232A 0xf29a
#define F0900_P2_CAR2S2_32ADERAT 0xf29a0040
#define F0900_P2_CAR2S2_32A_ALPH_M 0xf29a0030
#define F0900_P2_CAR2S2_32A_ALPH_E 0xf29a000f

/*P2_BCLC2S2Q*/
#define R0900_P2_BCLC2S2Q 0xf29c
#define F0900_P2_CAR2S2_Q_BETA_M 0xf29c0030
#define F0900_P2_CAR2S2_Q_BETA_E 0xf29c000f

/*P2_BCLC2S28*/
#define R0900_P2_BCLC2S28 0xf29d
#define F0900_P2_CAR2S2_8_BETA_M 0xf29d0030
#define F0900_P2_CAR2S2_8_BETA_E 0xf29d000f

/*P2_BCLC2S216A*/
#define R0900_P2_BCLC2S216A 0xf29e

/*P2_BCLC2S232A*/
#define R0900_P2_BCLC2S232A 0xf29f

/*P2_PLROOT2*/
#define R0900_P2_PLROOT2 0xf2ac
#define F0900_P2_PLSCRAMB_MODE 0xf2ac000c
#define F0900_P2_PLSCRAMB_ROOT2 0xf2ac0003

/*P2_PLROOT1*/
#define R0900_P2_PLROOT1 0xf2ad
#define F0900_P2_PLSCRAMB_ROOT1 0xf2ad00ff

/*P2_PLROOT0*/
#define R0900_P2_PLROOT0 0xf2ae
#define F0900_P2_PLSCRAMB_ROOT0 0xf2ae00ff

/*P2_MODCODLST0*/
#define R0900_P2_MODCODLST0 0xf2b0

/*P2_MODCODLST1*/
#define R0900_P2_MODCODLST1 0xf2b1
#define F0900_P2_DIS_MODCOD29 0xf2b100f0
#define F0900_P2_DIS_32PSK_9_10 0xf2b1000f

/*P2_MODCODLST2*/
#define R0900_P2_MODCODLST2 0xf2b2
#define F0900_P2_DIS_32PSK_8_9 0xf2b200f0
#define F0900_P2_DIS_32PSK_5_6 0xf2b2000f

/*P2_MODCODLST3*/
#define R0900_P2_MODCODLST3 0xf2b3
#define F0900_P2_DIS_32PSK_4_5 0xf2b300f0
#define F0900_P2_DIS_32PSK_3_4 0xf2b3000f

/*P2_MODCODLST4*/
#define R0900_P2_MODCODLST4 0xf2b4
#define F0900_P2_DIS_16PSK_9_10 0xf2b400f0
#define F0900_P2_DIS_16PSK_8_9 0xf2b4000f

/*P2_MODCODLST5*/
#define R0900_P2_MODCODLST5 0xf2b5
#define F0900_P2_DIS_16PSK_5_6 0xf2b500f0
#define F0900_P2_DIS_16PSK_4_5 0xf2b5000f

/*P2_MODCODLST6*/
#define R0900_P2_MODCODLST6 0xf2b6
#define F0900_P2_DIS_16PSK_3_4 0xf2b600f0
#define F0900_P2_DIS_16PSK_2_3 0xf2b6000f

/*P2_MODCODLST7*/
#define R0900_P2_MODCODLST7 0xf2b7
#define F0900_P2_DIS_8P_9_10 0xf2b700f0
#define F0900_P2_DIS_8P_8_9 0xf2b7000f

/*P2_MODCODLST8*/
#define R0900_P2_MODCODLST8 0xf2b8
#define F0900_P2_DIS_8P_5_6 0xf2b800f0
#define F0900_P2_DIS_8P_3_4 0xf2b8000f

/*P2_MODCODLST9*/
#define R0900_P2_MODCODLST9 0xf2b9
#define F0900_P2_DIS_8P_2_3 0xf2b900f0
#define F0900_P2_DIS_8P_3_5 0xf2b9000f

/*P2_MODCODLSTA*/
#define R0900_P2_MODCODLSTA 0xf2ba
#define F0900_P2_DIS_QP_9_10 0xf2ba00f0
#define F0900_P2_DIS_QP_8_9 0xf2ba000f

/*P2_MODCODLSTB*/
#define R0900_P2_MODCODLSTB 0xf2bb
#define F0900_P2_DIS_QP_5_6 0xf2bb00f0
#define F0900_P2_DIS_QP_4_5 0xf2bb000f

/*P2_MODCODLSTC*/
#define R0900_P2_MODCODLSTC 0xf2bc
#define F0900_P2_DIS_QP_3_4 0xf2bc00f0
#define F0900_P2_DIS_QP_2_3 0xf2bc000f

/*P2_MODCODLSTD*/
#define R0900_P2_MODCODLSTD 0xf2bd
#define F0900_P2_DIS_QP_3_5 0xf2bd00f0
#define F0900_P2_DIS_QP_1_2 0xf2bd000f

/*P2_MODCODLSTE*/
#define R0900_P2_MODCODLSTE 0xf2be
#define F0900_P2_DIS_QP_2_5 0xf2be00f0
#define F0900_P2_DIS_QP_1_3 0xf2be000f

/*P2_MODCODLSTF*/
#define R0900_P2_MODCODLSTF 0xf2bf
#define F0900_P2_DIS_QP_1_4 0xf2bf00f0

/*P2_GAUSSR0*/
#define R0900_P2_GAUSSR0 0xf2c0
#define F0900_P2_EN_CCIMODE 0xf2c00080
#define F0900_P2_R0_GAUSSIEN 0xf2c0007f

/*P2_CCIR0*/
#define R0900_P2_CCIR0 0xf2c1
#define F0900_P2_CCIDETECT_PLHONLY 0xf2c10080
#define F0900_P2_R0_CCI 0xf2c1007f

/*P2_CCIQUANT*/
#define R0900_P2_CCIQUANT 0xf2c2
#define F0900_P2_CCI_BETA 0xf2c200e0
#define F0900_P2_CCI_QUANT 0xf2c2001f

/*P2_CCITHRES*/
#define R0900_P2_CCITHRES 0xf2c3
#define F0900_P2_CCI_THRESHOLD 0xf2c300ff

/*P2_CCIACC*/
#define R0900_P2_CCIACC 0xf2c4
#define F0900_P2_CCI_VALUE 0xf2c400ff

/*P2_DMDRESCFG*/
#define R0900_P2_DMDRESCFG 0xf2c6
#define F0900_P2_DMDRES_RESET 0xf2c60080
#define F0900_P2_DMDRES_STRALL 0xf2c60008
#define F0900_P2_DMDRES_NEWONLY 0xf2c60004
#define F0900_P2_DMDRES_NOSTORE 0xf2c60002

/*P2_DMDRESADR*/
#define R0900_P2_DMDRESADR 0xf2c7
#define F0900_P2_DMDRES_VALIDCFR 0xf2c70040
#define F0900_P2_DMDRES_MEMFULL 0xf2c70030
#define F0900_P2_DMDRES_RESNBR 0xf2c7000f

/*P2_DMDRESDATA7*/
#define R0900_P2_DMDRESDATA7 0xf2c8
#define F0900_P2_DMDRES_DATA7 0xf2c800ff

/*P2_DMDRESDATA6*/
#define R0900_P2_DMDRESDATA6 0xf2c9
#define F0900_P2_DMDRES_DATA6 0xf2c900ff

/*P2_DMDRESDATA5*/
#define R0900_P2_DMDRESDATA5 0xf2ca
#define F0900_P2_DMDRES_DATA5 0xf2ca00ff

/*P2_DMDRESDATA4*/
#define R0900_P2_DMDRESDATA4 0xf2cb
#define F0900_P2_DMDRES_DATA4 0xf2cb00ff

/*P2_DMDRESDATA3*/
#define R0900_P2_DMDRESDATA3 0xf2cc
#define F0900_P2_DMDRES_DATA3 0xf2cc00ff

/*P2_DMDRESDATA2*/
#define R0900_P2_DMDRESDATA2 0xf2cd
#define F0900_P2_DMDRES_DATA2 0xf2cd00ff

/*P2_DMDRESDATA1*/
#define R0900_P2_DMDRESDATA1 0xf2ce
#define F0900_P2_DMDRES_DATA1 0xf2ce00ff

/*P2_DMDRESDATA0*/
#define R0900_P2_DMDRESDATA0 0xf2cf
#define F0900_P2_DMDRES_DATA0 0xf2cf00ff

/*P2_FFEI1*/
#define R0900_P2_FFEI1 0xf2d0
#define F0900_P2_FFE_ACCI1 0xf2d001ff

/*P2_FFEQ1*/
#define R0900_P2_FFEQ1 0xf2d1
#define F0900_P2_FFE_ACCQ1 0xf2d101ff

/*P2_FFEI2*/
#define R0900_P2_FFEI2 0xf2d2
#define F0900_P2_FFE_ACCI2 0xf2d201ff

/*P2_FFEQ2*/
#define R0900_P2_FFEQ2 0xf2d3
#define F0900_P2_FFE_ACCQ2 0xf2d301ff

/*P2_FFEI3*/
#define R0900_P2_FFEI3 0xf2d4
#define F0900_P2_FFE_ACCI3 0xf2d401ff

/*P2_FFEQ3*/
#define R0900_P2_FFEQ3 0xf2d5
#define F0900_P2_FFE_ACCQ3 0xf2d501ff

/*P2_FFEI4*/
#define R0900_P2_FFEI4 0xf2d6
#define F0900_P2_FFE_ACCI4 0xf2d601ff

/*P2_FFEQ4*/
#define R0900_P2_FFEQ4 0xf2d7
#define F0900_P2_FFE_ACCQ4 0xf2d701ff

/*P2_FFECFG*/
#define R0900_P2_FFECFG 0xf2d8
#define F0900_P2_EQUALFFE_ON 0xf2d80040
#define F0900_P2_MU_EQUALFFE 0xf2d80007

/*P2_TNRCFG*/
#define R0900_P2_TNRCFG 0xf2e0
#define F0900_P2_TUN_ACKFAIL 0xf2e00080
#define F0900_P2_TUN_TYPE 0xf2e00070
#define F0900_P2_TUN_SECSTOP 0xf2e00008
#define F0900_P2_TUN_VCOSRCH 0xf2e00004
#define F0900_P2_TUN_MADDRESS 0xf2e00003

/*P2_TNRCFG2*/
#define R0900_P2_TNRCFG2 0xf2e1
#define F0900_P2_TUN_IQSWAP 0xf2e10080
#define F0900_P2_DIS_BWCALC 0xf2e10004
#define F0900_P2_SHORT_WAITSTATES 0xf2e10002

/*P2_TNRXTAL*/
#define R0900_P2_TNRXTAL 0xf2e4
#define F0900_P2_TUN_XTALFREQ 0xf2e4001f

/*P2_TNRSTEPS*/
#define R0900_P2_TNRSTEPS 0xf2e7
#define F0900_P2_TUNER_BW0P125 0xf2e70080
#define F0900_P2_BWINC_OFFSET 0xf2e70170
#define F0900_P2_SOFTSTEP_RNG 0xf2e70008
#define F0900_P2_TUN_BWOFFSET 0xf2e70007

/*P2_TNRGAIN*/
#define R0900_P2_TNRGAIN 0xf2e8
#define F0900_P2_TUN_KDIVEN 0xf2e800c0
#define F0900_P2_STB6X00_OCK 0xf2e80030
#define F0900_P2_TUN_GAIN 0xf2e8000f

/*P2_TNRRF1*/
#define R0900_P2_TNRRF1 0xf2e9
#define F0900_P2_TUN_RFFREQ2 0xf2e900ff

/*P2_TNRRF0*/
#define R0900_P2_TNRRF0 0xf2ea
#define F0900_P2_TUN_RFFREQ1 0xf2ea00ff

/*P2_TNRBW*/
#define R0900_P2_TNRBW 0xf2eb
#define F0900_P2_TUN_RFFREQ0 0xf2eb00c0
#define F0900_P2_TUN_BW 0xf2eb003f

/*P2_TNRADJ*/
#define R0900_P2_TNRADJ 0xf2ec
#define F0900_P2_STB61X0_CALTIME 0xf2ec0040

/*P2_TNRCTL2*/
#define R0900_P2_TNRCTL2 0xf2ed
#define F0900_P2_STB61X0_RCCKOFF 0xf2ed0080
#define F0900_P2_STB61X0_ICP_SDOFF 0xf2ed0040
#define F0900_P2_STB61X0_DCLOOPOFF 0xf2ed0020
#define F0900_P2_STB61X0_REFOUTSEL 0xf2ed0010
#define F0900_P2_STB61X0_CALOFF 0xf2ed0008
#define F0900_P2_STB6XX0_LPT_BEN 0xf2ed0004
#define F0900_P2_STB6XX0_RX_OSCP 0xf2ed0002
#define F0900_P2_STB6XX0_SYN 0xf2ed0001

/*P2_TNRCFG3*/
#define R0900_P2_TNRCFG3 0xf2ee
#define F0900_P2_TUN_PLLFREQ 0xf2ee001c
#define F0900_P2_TUN_I2CFREQ_MODE 0xf2ee0003

/*P2_TNRLAUNCH*/
#define R0900_P2_TNRLAUNCH 0xf2f0

/*P2_TNRLD*/
#define R0900_P2_TNRLD 0xf2f0
#define F0900_P2_TUNLD_VCOING 0xf2f00080
#define F0900_P2_TUN_REG1FAIL 0xf2f00040
#define F0900_P2_TUN_REG2FAIL 0xf2f00020
#define F0900_P2_TUN_REG3FAIL 0xf2f00010
#define F0900_P2_TUN_REG4FAIL 0xf2f00008
#define F0900_P2_TUN_REG5FAIL 0xf2f00004
#define F0900_P2_TUN_BWING 0xf2f00002
#define F0900_P2_TUN_LOCKED 0xf2f00001

/*P2_TNROBSL*/
#define R0900_P2_TNROBSL 0xf2f6
#define F0900_P2_TUN_I2CABORTED 0xf2f60080
#define F0900_P2_TUN_LPEN 0xf2f60040
#define F0900_P2_TUN_FCCK 0xf2f60020
#define F0900_P2_TUN_I2CLOCKED 0xf2f60010
#define F0900_P2_TUN_PROGDONE 0xf2f6000c
#define F0900_P2_TUN_RFRESTE1 0xf2f60003

/*P2_TNRRESTE*/
#define R0900_P2_TNRRESTE 0xf2f7
#define F0900_P2_TUN_RFRESTE0 0xf2f700ff

/*P2_SMAPCOEF7*/
#define R0900_P2_SMAPCOEF7 0xf300
#define F0900_P2_DIS_QSCALE 0xf3000080
#define F0900_P2_SMAPCOEF_Q_LLR12 0xf300017f

/*P2_SMAPCOEF6*/
#define R0900_P2_SMAPCOEF6 0xf301
#define F0900_P2_ADJ_8PSKLLR1 0xf3010004
#define F0900_P2_OLD_8PSKLLR1 0xf3010002
#define F0900_P2_DIS_AB8PSK 0xf3010001

/*P2_SMAPCOEF5*/
#define R0900_P2_SMAPCOEF5 0xf302
#define F0900_P2_DIS_8SCALE 0xf3020080
#define F0900_P2_SMAPCOEF_8P_LLR23 0xf302017f

/*P2_NCO2MAX1*/
#define R0900_P2_NCO2MAX1 0xf314
#define F0900_P2_TETA2_MAXVABS1 0xf31400ff

/*P2_NCO2MAX0*/
#define R0900_P2_NCO2MAX0 0xf315
#define F0900_P2_TETA2_MAXVABS0 0xf31500ff

/*P2_NCO2FR1*/
#define R0900_P2_NCO2FR1 0xf316
#define F0900_P2_NCO2FINAL_ANGLE1 0xf31600ff

/*P2_NCO2FR0*/
#define R0900_P2_NCO2FR0 0xf317
#define F0900_P2_NCO2FINAL_ANGLE0 0xf31700ff

/*P2_CFR2AVRGE1*/
#define R0900_P2_CFR2AVRGE1 0xf318
#define F0900_P2_I2C_CFR2AVERAGE1 0xf31800ff

/*P2_CFR2AVRGE0*/
#define R0900_P2_CFR2AVRGE0 0xf319
#define F0900_P2_I2C_CFR2AVERAGE0 0xf31900ff

/*P2_DMDPLHSTAT*/
#define R0900_P2_DMDPLHSTAT 0xf320
#define F0900_P2_PLH_STATISTIC 0xf32000ff

/*P2_LOCKTIME3*/
#define R0900_P2_LOCKTIME3 0xf322
#define F0900_P2_DEMOD_LOCKTIME3 0xf32200ff

/*P2_LOCKTIME2*/
#define R0900_P2_LOCKTIME2 0xf323
#define F0900_P2_DEMOD_LOCKTIME2 0xf32300ff

/*P2_LOCKTIME1*/
#define R0900_P2_LOCKTIME1 0xf324
#define F0900_P2_DEMOD_LOCKTIME1 0xf32400ff

/*P2_LOCKTIME0*/
#define R0900_P2_LOCKTIME0 0xf325
#define F0900_P2_DEMOD_LOCKTIME0 0xf32500ff

/*P2_VITSCALE*/
#define R0900_P2_VITSCALE 0xf332
#define F0900_P2_NVTH_NOSRANGE 0xf3320080
#define F0900_P2_VERROR_MAXMODE 0xf3320040
#define F0900_P2_NSLOWSN_LOCKED 0xf3320008
#define F0900_P2_DIS_RSFLOCK 0xf3320002

/*P2_FECM*/
#define R0900_P2_FECM 0xf333
#define F0900_P2_DSS_DVB 0xf3330080
#define F0900_P2_DSS_SRCH 0xf3330010
#define F0900_P2_SYNCVIT 0xf3330002
#define F0900_P2_IQINV 0xf3330001

/*P2_VTH12*/
#define R0900_P2_VTH12 0xf334
#define F0900_P2_VTH12 0xf33400ff

/*P2_VTH23*/
#define R0900_P2_VTH23 0xf335
#define F0900_P2_VTH23 0xf33500ff

/*P2_VTH34*/
#define R0900_P2_VTH34 0xf336
#define F0900_P2_VTH34 0xf33600ff

/*P2_VTH56*/
#define R0900_P2_VTH56 0xf337
#define F0900_P2_VTH56 0xf33700ff

/*P2_VTH67*/
#define R0900_P2_VTH67 0xf338
#define F0900_P2_VTH67 0xf33800ff

/*P2_VTH78*/
#define R0900_P2_VTH78 0xf339
#define F0900_P2_VTH78 0xf33900ff

/*P2_VITCURPUN*/
#define R0900_P2_VITCURPUN 0xf33a
#define F0900_P2_VIT_CURPUN 0xf33a001f

/*P2_VERROR*/
#define R0900_P2_VERROR 0xf33b
#define F0900_P2_REGERR_VIT 0xf33b00ff

/*P2_PRVIT*/
#define R0900_P2_PRVIT 0xf33c
#define F0900_P2_DIS_VTHLOCK 0xf33c0040
#define F0900_P2_E7_8VIT 0xf33c0020
#define F0900_P2_E6_7VIT 0xf33c0010
#define F0900_P2_E5_6VIT 0xf33c0008
#define F0900_P2_E3_4VIT 0xf33c0004
#define F0900_P2_E2_3VIT 0xf33c0002
#define F0900_P2_E1_2VIT 0xf33c0001

/*P2_VAVSRVIT*/
#define R0900_P2_VAVSRVIT 0xf33d
#define F0900_P2_AMVIT 0xf33d0080
#define F0900_P2_FROZENVIT 0xf33d0040
#define F0900_P2_SNVIT 0xf33d0030
#define F0900_P2_TOVVIT 0xf33d000c
#define F0900_P2_HYPVIT 0xf33d0003

/*P2_VSTATUSVIT*/
#define R0900_P2_VSTATUSVIT 0xf33e
#define F0900_P2_PRFVIT 0xf33e0010
#define F0900_P2_LOCKEDVIT 0xf33e0008

/*P2_VTHINUSE*/
#define R0900_P2_VTHINUSE 0xf33f
#define F0900_P2_VIT_INUSE 0xf33f00ff

/*P2_KDIV12*/
#define R0900_P2_KDIV12 0xf340
#define F0900_P2_K_DIVIDER_12 0xf340007f

/*P2_KDIV23*/
#define R0900_P2_KDIV23 0xf341
#define F0900_P2_K_DIVIDER_23 0xf341007f

/*P2_KDIV34*/
#define R0900_P2_KDIV34 0xf342
#define F0900_P2_K_DIVIDER_34 0xf342007f

/*P2_KDIV56*/
#define R0900_P2_KDIV56 0xf343
#define F0900_P2_K_DIVIDER_56 0xf343007f

/*P2_KDIV67*/
#define R0900_P2_KDIV67 0xf344
#define F0900_P2_K_DIVIDER_67 0xf344007f

/*P2_KDIV78*/
#define R0900_P2_KDIV78 0xf345
#define F0900_P2_K_DIVIDER_78 0xf345007f

/*P2_PDELCTRL1*/
#define R0900_P2_PDELCTRL1 0xf350
#define F0900_P2_INV_MISMASK 0xf3500080
#define F0900_P2_FILTER_EN 0xf3500020
#define F0900_P2_EN_MIS00 0xf3500002
#define F0900_P2_ALGOSWRST 0xf3500001

/*P2_PDELCTRL2*/
#define R0900_P2_PDELCTRL2 0xf351
#define F0900_P2_RESET_UPKO_COUNT 0xf3510040
#define F0900_P2_FRAME_MODE 0xf3510002
#define F0900_P2_NOBCHERRFLG_USE 0xf3510001

/*P2_HYSTTHRESH*/
#define R0900_P2_HYSTTHRESH 0xf354
#define F0900_P2_UNLCK_THRESH 0xf35400f0
#define F0900_P2_DELIN_LCK_THRESH 0xf354000f

/*P2_ISIENTRY*/
#define R0900_P2_ISIENTRY 0xf35e
#define F0900_P2_ISI_ENTRY 0xf35e00ff

/*P2_ISIBITENA*/
#define R0900_P2_ISIBITENA 0xf35f
#define F0900_P2_ISI_BIT_EN 0xf35f00ff

/*P2_MATSTR1*/
#define R0900_P2_MATSTR1 0xf360
#define F0900_P2_MATYPE_CURRENT1 0xf36000ff

/*P2_MATSTR0*/
#define R0900_P2_MATSTR0 0xf361
#define F0900_P2_MATYPE_CURRENT0 0xf36100ff

/*P2_UPLSTR1*/
#define R0900_P2_UPLSTR1 0xf362
#define F0900_P2_UPL_CURRENT1 0xf36200ff

/*P2_UPLSTR0*/
#define R0900_P2_UPLSTR0 0xf363
#define F0900_P2_UPL_CURRENT0 0xf36300ff

/*P2_DFLSTR1*/
#define R0900_P2_DFLSTR1 0xf364
#define F0900_P2_DFL_CURRENT1 0xf36400ff

/*P2_DFLSTR0*/
#define R0900_P2_DFLSTR0 0xf365
#define F0900_P2_DFL_CURRENT0 0xf36500ff

/*P2_SYNCSTR*/
#define R0900_P2_SYNCSTR 0xf366
#define F0900_P2_SYNC_CURRENT 0xf36600ff

/*P2_SYNCDSTR1*/
#define R0900_P2_SYNCDSTR1 0xf367
#define F0900_P2_SYNCD_CURRENT1 0xf36700ff

/*P2_SYNCDSTR0*/
#define R0900_P2_SYNCDSTR0 0xf368
#define F0900_P2_SYNCD_CURRENT0 0xf36800ff

/*P2_PDELSTATUS1*/
#define R0900_P2_PDELSTATUS1 0xf369
#define F0900_P2_PKTDELIN_DELOCK 0xf3690080
#define F0900_P2_SYNCDUPDFL_BADDFL 0xf3690040
#define F0900_P2_CONTINUOUS_STREAM 0xf3690020
#define F0900_P2_UNACCEPTED_STREAM 0xf3690010
#define F0900_P2_BCH_ERROR_FLAG 0xf3690008
#define F0900_P2_PKTDELIN_LOCK 0xf3690002
#define F0900_P2_FIRST_LOCK 0xf3690001

/*P2_PDELSTATUS2*/
#define R0900_P2_PDELSTATUS2 0xf36a
#define F0900_P2_FRAME_MODCOD 0xf36a007c
#define F0900_P2_FRAME_TYPE 0xf36a0003

/*P2_BBFCRCKO1*/
#define R0900_P2_BBFCRCKO1 0xf36b
#define F0900_P2_BBHCRC_KOCNT1 0xf36b00ff

/*P2_BBFCRCKO0*/
#define R0900_P2_BBFCRCKO0 0xf36c
#define F0900_P2_BBHCRC_KOCNT0 0xf36c00ff

/*P2_UPCRCKO1*/
#define R0900_P2_UPCRCKO1 0xf36d
#define F0900_P2_PKTCRC_KOCNT1 0xf36d00ff

/*P2_UPCRCKO0*/
#define R0900_P2_UPCRCKO0 0xf36e
#define F0900_P2_PKTCRC_KOCNT0 0xf36e00ff

/*P2_PDELCTRL3*/
#define R0900_P2_PDELCTRL3 0xf36f
#define F0900_P2_PKTDEL_CONTFAIL 0xf36f0080
#define F0900_P2_NOFIFO_BCHERR 0xf36f0020

/*P2_TSSTATEM*/
#define R0900_P2_TSSTATEM 0xf370
#define F0900_P2_TSDIL_ON 0xf3700080
#define F0900_P2_TSRS_ON 0xf3700020
#define F0900_P2_TSDESCRAMB_ON 0xf3700010
#define F0900_P2_TSFRAME_MODE 0xf3700008
#define F0900_P2_TS_DISABLE 0xf3700004
#define F0900_P2_TSOUT_NOSYNC 0xf3700001

/*P2_TSCFGH*/
#define R0900_P2_TSCFGH 0xf372
#define F0900_P2_TSFIFO_DVBCI 0xf3720080
#define F0900_P2_TSFIFO_SERIAL 0xf3720040
#define F0900_P2_TSFIFO_TEIUPDATE 0xf3720020
#define F0900_P2_TSFIFO_DUTY50 0xf3720010
#define F0900_P2_TSFIFO_HSGNLOUT 0xf3720008
#define F0900_P2_TSFIFO_ERRMODE 0xf3720006
#define F0900_P2_RST_HWARE 0xf3720001

/*P2_TSCFGM*/
#define R0900_P2_TSCFGM 0xf373
#define F0900_P2_TSFIFO_MANSPEED 0xf37300c0
#define F0900_P2_TSFIFO_PERMDATA 0xf3730020
#define F0900_P2_TSFIFO_DPUNACT 0xf3730002
#define F0900_P2_TSFIFO_INVDATA 0xf3730001

/*P2_TSCFGL*/
#define R0900_P2_TSCFGL 0xf374
#define F0900_P2_TSFIFO_BCLKDEL1CK 0xf37400c0
#define F0900_P2_BCHERROR_MODE 0xf3740030
#define F0900_P2_TSFIFO_NSGNL2DATA 0xf3740008
#define F0900_P2_TSFIFO_EMBINDVB 0xf3740004
#define F0900_P2_TSFIFO_BITSPEED 0xf3740003

/*P2_TSINSDELH*/
#define R0900_P2_TSINSDELH 0xf376
#define F0900_P2_TSDEL_SYNCBYTE 0xf3760080
#define F0900_P2_TSDEL_XXHEADER 0xf3760040
#define F0900_P2_TSDEL_BBHEADER 0xf3760020
#define F0900_P2_TSDEL_DATAFIELD 0xf3760010
#define F0900_P2_TSINSDEL_ISCR 0xf3760008
#define F0900_P2_TSINSDEL_NPD 0xf3760004
#define F0900_P2_TSINSDEL_RSPARITY 0xf3760002
#define F0900_P2_TSINSDEL_CRC8 0xf3760001

/*P2_TSDIVN*/
#define R0900_P2_TSDIVN 0xf379
#define F0900_P2_TSFIFO_SPEEDMODE 0xf37900c0

/*P2_TSCFG4*/
#define R0900_P2_TSCFG4 0xf37a
#define F0900_P2_TSFIFO_TSSPEEDMODE 0xf37a00c0

/*P2_TSSPEED*/
#define R0900_P2_TSSPEED 0xf380
#define F0900_P2_TSFIFO_OUTSPEED 0xf38000ff

/*P2_TSSTATUS*/
#define R0900_P2_TSSTATUS 0xf381
#define F0900_P2_TSFIFO_LINEOK 0xf3810080
#define F0900_P2_TSFIFO_ERROR 0xf3810040
#define F0900_P2_DIL_READY 0xf3810001

/*P2_TSSTATUS2*/
#define R0900_P2_TSSTATUS2 0xf382
#define F0900_P2_TSFIFO_DEMODSEL 0xf3820080
#define F0900_P2_TSFIFOSPEED_STORE 0xf3820040
#define F0900_P2_DILXX_RESET 0xf3820020
#define F0900_P2_TSSERIAL_IMPOS 0xf3820010
#define F0900_P2_SCRAMBDETECT 0xf3820002

/*P2_TSBITRATE1*/
#define R0900_P2_TSBITRATE1 0xf383
#define F0900_P2_TSFIFO_BITRATE1 0xf38300ff

/*P2_TSBITRATE0*/
#define R0900_P2_TSBITRATE0 0xf384
#define F0900_P2_TSFIFO_BITRATE0 0xf38400ff

/*P2_ERRCTRL1*/
#define R0900_P2_ERRCTRL1 0xf398
#define F0900_P2_ERR_SOURCE1 0xf39800f0
#define F0900_P2_NUM_EVENT1 0xf3980007

/*P2_ERRCNT12*/
#define R0900_P2_ERRCNT12 0xf399
#define F0900_P2_ERRCNT1_OLDVALUE 0xf3990080
#define F0900_P2_ERR_CNT12 0xf399007f

/*P2_ERRCNT11*/
#define R0900_P2_ERRCNT11 0xf39a
#define F0900_P2_ERR_CNT11 0xf39a00ff

/*P2_ERRCNT10*/
#define R0900_P2_ERRCNT10 0xf39b
#define F0900_P2_ERR_CNT10 0xf39b00ff

/*P2_ERRCTRL2*/
#define R0900_P2_ERRCTRL2 0xf39c
#define F0900_P2_ERR_SOURCE2 0xf39c00f0
#define F0900_P2_NUM_EVENT2 0xf39c0007

/*P2_ERRCNT22*/
#define R0900_P2_ERRCNT22 0xf39d
#define F0900_P2_ERRCNT2_OLDVALUE 0xf39d0080
#define F0900_P2_ERR_CNT22 0xf39d007f

/*P2_ERRCNT21*/
#define R0900_P2_ERRCNT21 0xf39e
#define F0900_P2_ERR_CNT21 0xf39e00ff

/*P2_ERRCNT20*/
#define R0900_P2_ERRCNT20 0xf39f
#define F0900_P2_ERR_CNT20 0xf39f00ff

/*P2_FECSPY*/
#define R0900_P2_FECSPY 0xf3a0
#define F0900_P2_SPY_ENABLE 0xf3a00080
#define F0900_P2_NO_SYNCBYTE 0xf3a00040
#define F0900_P2_SERIAL_MODE 0xf3a00020
#define F0900_P2_UNUSUAL_PACKET 0xf3a00010
#define F0900_P2_BERMETER_DATAMODE 0xf3a00008
#define F0900_P2_BERMETER_LMODE 0xf3a00002
#define F0900_P2_BERMETER_RESET 0xf3a00001

/*P2_FSPYCFG*/
#define R0900_P2_FSPYCFG 0xf3a1
#define F0900_P2_FECSPY_INPUT 0xf3a100c0
#define F0900_P2_RST_ON_ERROR 0xf3a10020
#define F0900_P2_ONE_SHOT 0xf3a10010
#define F0900_P2_I2C_MODE 0xf3a1000c
#define F0900_P2_SPY_HYSTERESIS 0xf3a10003

/*P2_FSPYDATA*/
#define R0900_P2_FSPYDATA 0xf3a2
#define F0900_P2_SPY_STUFFING 0xf3a20080
#define F0900_P2_SPY_CNULLPKT 0xf3a20020
#define F0900_P2_SPY_OUTDATA_MODE 0xf3a2001f

/*P2_FSPYOUT*/
#define R0900_P2_FSPYOUT 0xf3a3
#define F0900_P2_FSPY_DIRECT 0xf3a30080
#define F0900_P2_STUFF_MODE 0xf3a30007

/*P2_FSTATUS*/
#define R0900_P2_FSTATUS 0xf3a4
#define F0900_P2_SPY_ENDSIM 0xf3a40080
#define F0900_P2_VALID_SIM 0xf3a40040
#define F0900_P2_FOUND_SIGNAL 0xf3a40020
#define F0900_P2_DSS_SYNCBYTE 0xf3a40010
#define F0900_P2_RESULT_STATE 0xf3a4000f

/*P2_FBERCPT4*/
#define R0900_P2_FBERCPT4 0xf3a8
#define F0900_P2_FBERMETER_CPT4 0xf3a800ff

/*P2_FBERCPT3*/
#define R0900_P2_FBERCPT3 0xf3a9
#define F0900_P2_FBERMETER_CPT3 0xf3a900ff

/*P2_FBERCPT2*/
#define R0900_P2_FBERCPT2 0xf3aa
#define F0900_P2_FBERMETER_CPT2 0xf3aa00ff

/*P2_FBERCPT1*/
#define R0900_P2_FBERCPT1 0xf3ab
#define F0900_P2_FBERMETER_CPT1 0xf3ab00ff

/*P2_FBERCPT0*/
#define R0900_P2_FBERCPT0 0xf3ac
#define F0900_P2_FBERMETER_CPT0 0xf3ac00ff

/*P2_FBERERR2*/
#define R0900_P2_FBERERR2 0xf3ad
#define F0900_P2_FBERMETER_ERR2 0xf3ad00ff

/*P2_FBERERR1*/
#define R0900_P2_FBERERR1 0xf3ae
#define F0900_P2_FBERMETER_ERR1 0xf3ae00ff

/*P2_FBERERR0*/
#define R0900_P2_FBERERR0 0xf3af
#define F0900_P2_FBERMETER_ERR0 0xf3af00ff

/*P2_FSPYBER*/
#define R0900_P2_FSPYBER 0xf3b2
#define F0900_P2_FSPYBER_SYNCBYTE 0xf3b20010
#define F0900_P2_FSPYBER_UNSYNC 0xf3b20008
#define F0900_P2_FSPYBER_CTIME 0xf3b20007

/*P1_IQCONST*/
#define R0900_P1_IQCONST 0xf400
#define IQCONST REGx(R0900_P1_IQCONST)
#define F0900_P1_CONSTEL_SELECT 0xf4000060
#define F0900_P1_IQSYMB_SEL 0xf400001f

/*P1_NOSCFG*/
#define R0900_P1_NOSCFG 0xf401
#define NOSCFG REGx(R0900_P1_NOSCFG)
#define F0900_P1_DUMMYPL_NOSDATA 0xf4010020
#define F0900_P1_NOSPLH_BETA 0xf4010018
#define F0900_P1_NOSDATA_BETA 0xf4010007

/*P1_ISYMB*/
#define R0900_P1_ISYMB 0xf402
#define ISYMB REGx(R0900_P1_ISYMB)
#define F0900_P1_I_SYMBOL 0xf40201ff

/*P1_QSYMB*/
#define R0900_P1_QSYMB 0xf403
#define QSYMB REGx(R0900_P1_QSYMB)
#define F0900_P1_Q_SYMBOL 0xf40301ff

/*P1_AGC1CFG*/
#define R0900_P1_AGC1CFG 0xf404
#define AGC1CFG REGx(R0900_P1_AGC1CFG)
#define F0900_P1_DC_FROZEN 0xf4040080
#define F0900_P1_DC_CORRECT 0xf4040040
#define F0900_P1_AMM_FROZEN 0xf4040020
#define F0900_P1_AMM_CORRECT 0xf4040010
#define F0900_P1_QUAD_FROZEN 0xf4040008
#define F0900_P1_QUAD_CORRECT 0xf4040004

/*P1_AGC1CN*/
#define R0900_P1_AGC1CN 0xf406
#define AGC1CN REGx(R0900_P1_AGC1CN)
#define F0900_P1_AGC1_LOCKED 0xf4060080
#define F0900_P1_AGC1_MINPOWER 0xf4060010
#define F0900_P1_AGCOUT_FAST 0xf4060008
#define F0900_P1_AGCIQ_BETA 0xf4060007

/*P1_AGC1REF*/
#define R0900_P1_AGC1REF 0xf407
#define AGC1REF REGx(R0900_P1_AGC1REF)
#define F0900_P1_AGCIQ_REF 0xf40700ff

/*P1_IDCCOMP*/
#define R0900_P1_IDCCOMP 0xf408
#define IDCCOMP REGx(R0900_P1_IDCCOMP)
#define F0900_P1_IAVERAGE_ADJ 0xf40801ff

/*P1_QDCCOMP*/
#define R0900_P1_QDCCOMP 0xf409
#define QDCCOMP REGx(R0900_P1_QDCCOMP)
#define F0900_P1_QAVERAGE_ADJ 0xf40901ff

/*P1_POWERI*/
#define R0900_P1_POWERI 0xf40a
#define POWERI REGx(R0900_P1_POWERI)
#define F0900_P1_POWER_I 0xf40a00ff
#define POWER_I FLDx(F0900_P1_POWER_I)

/*P1_POWERQ*/
#define R0900_P1_POWERQ 0xf40b
#define POWERQ REGx(R0900_P1_POWERQ)
#define F0900_P1_POWER_Q 0xf40b00ff
#define POWER_Q FLDx(F0900_P1_POWER_Q)

/*P1_AGC1AMM*/
#define R0900_P1_AGC1AMM 0xf40c
#define AGC1AMM REGx(R0900_P1_AGC1AMM)
#define F0900_P1_AMM_VALUE 0xf40c00ff

/*P1_AGC1QUAD*/
#define R0900_P1_AGC1QUAD 0xf40d
#define AGC1QUAD REGx(R0900_P1_AGC1QUAD)
#define F0900_P1_QUAD_VALUE 0xf40d01ff

/*P1_AGCIQIN1*/
#define R0900_P1_AGCIQIN1 0xf40e
#define AGCIQIN1 REGx(R0900_P1_AGCIQIN1)
#define F0900_P1_AGCIQ_VALUE1 0xf40e00ff
#define AGCIQ_VALUE1 FLDx(F0900_P1_AGCIQ_VALUE1)

/*P1_AGCIQIN0*/
#define R0900_P1_AGCIQIN0 0xf40f
#define AGCIQIN0 REGx(R0900_P1_AGCIQIN0)
#define F0900_P1_AGCIQ_VALUE0 0xf40f00ff
#define AGCIQ_VALUE0 FLDx(F0900_P1_AGCIQ_VALUE0)

/*P1_DEMOD*/
#define R0900_P1_DEMOD 0xf410
#define DEMOD REGx(R0900_P1_DEMOD)
#define F0900_P1_MANUALS2_ROLLOFF 0xf4100080
#define MANUALS2_ROLLOFF FLDx(F0900_P1_MANUALS2_ROLLOFF)

#define F0900_P1_SPECINV_CONTROL 0xf4100030
#define SPECINV_CONTROL FLDx(F0900_P1_SPECINV_CONTROL)
#define F0900_P1_FORCE_ENASAMP 0xf4100008
#define F0900_P1_MANUALSX_ROLLOFF 0xf4100004
#define MANUALSX_ROLLOFF FLDx(F0900_P1_MANUALSX_ROLLOFF)
#define F0900_P1_ROLLOFF_CONTROL 0xf4100003
#define ROLLOFF_CONTROL FLDx(F0900_P1_ROLLOFF_CONTROL)

/*P1_DMDMODCOD*/
#define R0900_P1_DMDMODCOD 0xf411
#define DMDMODCOD REGx(R0900_P1_DMDMODCOD)
#define F0900_P1_MANUAL_MODCOD 0xf4110080
#define F0900_P1_DEMOD_MODCOD 0xf411007c
#define DEMOD_MODCOD FLDx(F0900_P1_DEMOD_MODCOD)
#define F0900_P1_DEMOD_TYPE 0xf4110003
#define DEMOD_TYPE FLDx(F0900_P1_DEMOD_TYPE)

/*P1_DSTATUS*/
#define R0900_P1_DSTATUS 0xf412
#define DSTATUS REGx(R0900_P1_DSTATUS)
#define F0900_P1_CAR_LOCK 0xf4120080
#define F0900_P1_TMGLOCK_QUALITY 0xf4120060
#define TMGLOCK_QUALITY FLDx(F0900_P1_TMGLOCK_QUALITY)
#define F0900_P1_LOCK_DEFINITIF 0xf4120008
#define LOCK_DEFINITIF FLDx(F0900_P1_LOCK_DEFINITIF)
#define F0900_P1_OVADC_DETECT 0xf4120001

/*P1_DSTATUS2*/
#define R0900_P1_DSTATUS2 0xf413
#define DSTATUS2 REGx(R0900_P1_DSTATUS2)
#define F0900_P1_DEMOD_DELOCK 0xf4130080
#define F0900_P1_AGC1_NOSIGNALACK 0xf4130008
#define F0900_P1_AGC2_OVERFLOW 0xf4130004
#define F0900_P1_CFR_OVERFLOW 0xf4130002
#define F0900_P1_GAMMA_OVERUNDER 0xf4130001

/*P1_DMDCFGMD*/
#define R0900_P1_DMDCFGMD 0xf414
#define DMDCFGMD REGx(R0900_P1_DMDCFGMD)
#define F0900_P1_DVBS2_ENABLE 0xf4140080
#define DVBS2_ENABLE FLDx(F0900_P1_DVBS2_ENABLE)
#define F0900_P1_DVBS1_ENABLE 0xf4140040
#define DVBS1_ENABLE FLDx(F0900_P1_DVBS1_ENABLE)
#define F0900_P1_SCAN_ENABLE 0xf4140010
#define SCAN_ENABLE FLDx(F0900_P1_SCAN_ENABLE)
#define F0900_P1_CFR_AUTOSCAN 0xf4140008
#define CFR_AUTOSCAN FLDx(F0900_P1_CFR_AUTOSCAN)
#define F0900_P1_TUN_RNG 0xf4140003

/*P1_DMDCFG2*/
#define R0900_P1_DMDCFG2 0xf415
#define DMDCFG2 REGx(R0900_P1_DMDCFG2)
#define F0900_P1_S1S2_SEQUENTIAL 0xf4150040
#define S1S2_SEQUENTIAL FLDx(F0900_P1_S1S2_SEQUENTIAL)
#define F0900_P1_INFINITE_RELOCK 0xf4150010

/*P1_DMDISTATE*/
#define R0900_P1_DMDISTATE 0xf416
#define DMDISTATE REGx(R0900_P1_DMDISTATE)
#define F0900_P1_I2C_DEMOD_MODE 0xf416001f
#define DEMOD_MODE FLDx(F0900_P1_I2C_DEMOD_MODE)

/*P1_DMDT0M*/
#define R0900_P1_DMDT0M 0xf417
#define DMDT0M REGx(R0900_P1_DMDT0M)
#define F0900_P1_DMDT0_MIN 0xf41700ff

/*P1_DMDSTATE*/
#define R0900_P1_DMDSTATE 0xf41b
#define DMDSTATE REGx(R0900_P1_DMDSTATE)
#define F0900_P1_HEADER_MODE 0xf41b0060
#define HEADER_MODE FLDx(F0900_P1_HEADER_MODE)

/*P1_DMDFLYW*/
#define R0900_P1_DMDFLYW 0xf41c
#define DMDFLYW REGx(R0900_P1_DMDFLYW)
#define F0900_P1_I2C_IRQVAL 0xf41c00f0
#define F0900_P1_FLYWHEEL_CPT 0xf41c000f
#define FLYWHEEL_CPT FLDx(F0900_P1_FLYWHEEL_CPT)

/*P1_DSTATUS3*/
#define R0900_P1_DSTATUS3 0xf41d
#define DSTATUS3 REGx(R0900_P1_DSTATUS3)
#define F0900_P1_DEMOD_CFGMODE 0xf41d0060

/*P1_DMDCFG3*/
#define R0900_P1_DMDCFG3 0xf41e
#define DMDCFG3 REGx(R0900_P1_DMDCFG3)
#define F0900_P1_NOSTOP_FIFOFULL 0xf41e0008

/*P1_DMDCFG4*/
#define R0900_P1_DMDCFG4 0xf41f
#define DMDCFG4 REGx(R0900_P1_DMDCFG4)
#define F0900_P1_TUNER_NRELAUNCH 0xf41f0008

/*P1_CORRELMANT*/
#define R0900_P1_CORRELMANT 0xf420
#define CORRELMANT REGx(R0900_P1_CORRELMANT)
#define F0900_P1_CORREL_MANT 0xf42000ff

/*P1_CORRELABS*/
#define R0900_P1_CORRELABS 0xf421
#define CORRELABS REGx(R0900_P1_CORRELABS)
#define F0900_P1_CORREL_ABS 0xf42100ff

/*P1_CORRELEXP*/
#define R0900_P1_CORRELEXP 0xf422
#define CORRELEXP REGx(R0900_P1_CORRELEXP)
#define F0900_P1_CORREL_ABSEXP 0xf42200f0
#define F0900_P1_CORREL_EXP 0xf422000f

/*P1_PLHMODCOD*/
#define R0900_P1_PLHMODCOD 0xf424
#define PLHMODCOD REGx(R0900_P1_PLHMODCOD)
#define F0900_P1_SPECINV_DEMOD 0xf4240080
#define SPECINV_DEMOD FLDx(F0900_P1_SPECINV_DEMOD)
#define F0900_P1_PLH_MODCOD 0xf424007c
#define F0900_P1_PLH_TYPE 0xf4240003

/*P1_DMDREG*/
#define R0900_P1_DMDREG 0xf425
#define DMDREG REGx(R0900_P1_DMDREG)
#define F0900_P1_DECIM_PLFRAMES 0xf4250001

/*P1_AGC2O*/
#define R0900_P1_AGC2O 0xf42c
#define AGC2O REGx(R0900_P1_AGC2O)
#define F0900_P1_AGC2_COEF 0xf42c0007

/*P1_AGC2REF*/
#define R0900_P1_AGC2REF 0xf42d
#define AGC2REF REGx(R0900_P1_AGC2REF)
#define F0900_P1_AGC2_REF 0xf42d00ff

/*P1_AGC1ADJ*/
#define R0900_P1_AGC1ADJ 0xf42e
#define AGC1ADJ REGx(R0900_P1_AGC1ADJ)
#define F0900_P1_AGC1_ADJUSTED 0xf42e007f

/*P1_AGC2I1*/
#define R0900_P1_AGC2I1 0xf436
#define AGC2I1 REGx(R0900_P1_AGC2I1)
#define F0900_P1_AGC2_INTEGRATOR1 0xf43600ff

/*P1_AGC2I0*/
#define R0900_P1_AGC2I0 0xf437
#define AGC2I0 REGx(R0900_P1_AGC2I0)
#define F0900_P1_AGC2_INTEGRATOR0 0xf43700ff

/*P1_CARCFG*/
#define R0900_P1_CARCFG 0xf438
#define CARCFG REGx(R0900_P1_CARCFG)
#define F0900_P1_CFRUPLOW_AUTO 0xf4380080
#define F0900_P1_CFRUPLOW_TEST 0xf4380040
#define F0900_P1_ROTAON 0xf4380004
#define F0900_P1_PH_DET_ALGO 0xf4380003

/*P1_ACLC*/
#define R0900_P1_ACLC 0xf439
#define ACLC REGx(R0900_P1_ACLC)
#define F0900_P1_CAR_ALPHA_MANT 0xf4390030
#define F0900_P1_CAR_ALPHA_EXP 0xf439000f

/*P1_BCLC*/
#define R0900_P1_BCLC 0xf43a
#define BCLC REGx(R0900_P1_BCLC)
#define F0900_P1_CAR_BETA_MANT 0xf43a0030
#define F0900_P1_CAR_BETA_EXP 0xf43a000f

/*P1_CARFREQ*/
#define R0900_P1_CARFREQ 0xf43d
#define CARFREQ REGx(R0900_P1_CARFREQ)
#define F0900_P1_KC_COARSE_EXP 0xf43d00f0
#define F0900_P1_BETA_FREQ 0xf43d000f

/*P1_CARHDR*/
#define R0900_P1_CARHDR 0xf43e
#define CARHDR REGx(R0900_P1_CARHDR)
#define F0900_P1_K_FREQ_HDR 0xf43e00ff

/*P1_LDT*/
#define R0900_P1_LDT 0xf43f
#define LDT REGx(R0900_P1_LDT)
#define F0900_P1_CARLOCK_THRES 0xf43f01ff

/*P1_LDT2*/
#define R0900_P1_LDT2 0xf440
#define LDT2 REGx(R0900_P1_LDT2)
#define F0900_P1_CARLOCK_THRES2 0xf44001ff

/*P1_CFRICFG*/
#define R0900_P1_CFRICFG 0xf441
#define CFRICFG REGx(R0900_P1_CFRICFG)
#define F0900_P1_NEG_CFRSTEP 0xf4410001

/*P1_CFRUP1*/
#define R0900_P1_CFRUP1 0xf442
#define CFRUP1 REGx(R0900_P1_CFRUP1)
#define F0900_P1_CFR_UP1 0xf44201ff
#define CFR_UP1 FLDx(F0900_P1_CFR_UP1)

/*P1_CFRUP0*/
#define R0900_P1_CFRUP0 0xf443
#define CFRUP0 REGx(R0900_P1_CFRUP0)
#define F0900_P1_CFR_UP0 0xf44300ff
#define CFR_UP0 FLDx(F0900_P1_CFR_UP0)

/*P1_CFRLOW1*/
#define R0900_P1_CFRLOW1 0xf446
#define CFRLOW1 REGx(R0900_P1_CFRLOW1)
#define F0900_P1_CFR_LOW1 0xf44601ff
#define CFR_LOW1 FLDx(F0900_P1_CFR_LOW1)

/*P1_CFRLOW0*/
#define R0900_P1_CFRLOW0 0xf447
#define CFRLOW0 REGx(R0900_P1_CFRLOW0)
#define F0900_P1_CFR_LOW0 0xf44700ff
#define CFR_LOW0 FLDx(F0900_P1_CFR_LOW0)

/*P1_CFRINIT1*/
#define R0900_P1_CFRINIT1 0xf448
#define CFRINIT1 REGx(R0900_P1_CFRINIT1)
#define F0900_P1_CFR_INIT1 0xf44801ff
#define CFR_INIT1 FLDx(F0900_P1_CFR_INIT1)

/*P1_CFRINIT0*/
#define R0900_P1_CFRINIT0 0xf449
#define CFRINIT0 REGx(R0900_P1_CFRINIT0)
#define F0900_P1_CFR_INIT0 0xf44900ff
#define CFR_INIT0 FLDx(F0900_P1_CFR_INIT0)

/*P1_CFRINC1*/
#define R0900_P1_CFRINC1 0xf44a
#define CFRINC1 REGx(R0900_P1_CFRINC1)
#define F0900_P1_MANUAL_CFRINC 0xf44a0080
#define F0900_P1_CFR_INC1 0xf44a003f

/*P1_CFRINC0*/
#define R0900_P1_CFRINC0 0xf44b
#define CFRINC0 REGx(R0900_P1_CFRINC0)
#define F0900_P1_CFR_INC0 0xf44b00f8

/*P1_CFR2*/
#define R0900_P1_CFR2 0xf44c
#define CFR2 REGx(R0900_P1_CFR2)
#define F0900_P1_CAR_FREQ2 0xf44c01ff
#define CAR_FREQ2 FLDx(F0900_P1_CAR_FREQ2)

/*P1_CFR1*/
#define R0900_P1_CFR1 0xf44d
#define CFR1 REGx(R0900_P1_CFR1)
#define F0900_P1_CAR_FREQ1 0xf44d00ff
#define CAR_FREQ1 FLDx(F0900_P1_CAR_FREQ1)

/*P1_CFR0*/
#define R0900_P1_CFR0 0xf44e
#define CFR0 REGx(R0900_P1_CFR0)
#define F0900_P1_CAR_FREQ0 0xf44e00ff
#define CAR_FREQ0 FLDx(F0900_P1_CAR_FREQ0)

/*P1_LDI*/
#define R0900_P1_LDI 0xf44f
#define LDI REGx(R0900_P1_LDI)
#define F0900_P1_LOCK_DET_INTEGR 0xf44f01ff

/*P1_TMGCFG*/
#define R0900_P1_TMGCFG 0xf450
#define TMGCFG REGx(R0900_P1_TMGCFG)
#define F0900_P1_TMGLOCK_BETA 0xf45000c0
#define F0900_P1_DO_TIMING_CORR 0xf4500010
#define F0900_P1_TMG_MINFREQ 0xf4500003

/*P1_RTC*/
#define R0900_P1_RTC 0xf451
#define RTC REGx(R0900_P1_RTC)
#define F0900_P1_TMGALPHA_EXP 0xf45100f0
#define F0900_P1_TMGBETA_EXP 0xf451000f

/*P1_RTCS2*/
#define R0900_P1_RTCS2 0xf452
#define RTCS2 REGx(R0900_P1_RTCS2)
#define F0900_P1_TMGALPHAS2_EXP 0xf45200f0
#define F0900_P1_TMGBETAS2_EXP 0xf452000f

/*P1_TMGTHRISE*/
#define R0900_P1_TMGTHRISE 0xf453
#define TMGTHRISE REGx(R0900_P1_TMGTHRISE)
#define F0900_P1_TMGLOCK_THRISE 0xf45300ff

/*P1_TMGTHFALL*/
#define R0900_P1_TMGTHFALL 0xf454
#define TMGTHFALL REGx(R0900_P1_TMGTHFALL)
#define F0900_P1_TMGLOCK_THFALL 0xf45400ff

/*P1_SFRUPRATIO*/
#define R0900_P1_SFRUPRATIO 0xf455
#define SFRUPRATIO REGx(R0900_P1_SFRUPRATIO)
#define F0900_P1_SFR_UPRATIO 0xf45500ff

/*P1_SFRLOWRATIO*/
#define R0900_P1_SFRLOWRATIO 0xf456
#define F0900_P1_SFR_LOWRATIO 0xf45600ff

/*P1_KREFTMG*/
#define R0900_P1_KREFTMG 0xf458
#define KREFTMG REGx(R0900_P1_KREFTMG)
#define F0900_P1_KREF_TMG 0xf45800ff

/*P1_SFRSTEP*/
#define R0900_P1_SFRSTEP 0xf459
#define SFRSTEP REGx(R0900_P1_SFRSTEP)
#define F0900_P1_SFR_SCANSTEP 0xf45900f0
#define F0900_P1_SFR_CENTERSTEP 0xf459000f

/*P1_TMGCFG2*/
#define R0900_P1_TMGCFG2 0xf45a
#define TMGCFG2 REGx(R0900_P1_TMGCFG2)
#define F0900_P1_SFRRATIO_FINE 0xf45a0001

/*P1_KREFTMG2*/
#define R0900_P1_KREFTMG2 0xf45b
#define KREFTMG2 REGx(R0900_P1_KREFTMG2)
#define F0900_P1_KREF_TMG2 0xf45b00ff

/*P1_SFRINIT1*/
#define R0900_P1_SFRINIT1 0xf45e
#define SFRINIT1 REGx(R0900_P1_SFRINIT1)
#define F0900_P1_SFR_INIT1 0xf45e007f

/*P1_SFRINIT0*/
#define R0900_P1_SFRINIT0 0xf45f
#define SFRINIT0 REGx(R0900_P1_SFRINIT0)
#define F0900_P1_SFR_INIT0 0xf45f00ff

/*P1_SFRUP1*/
#define R0900_P1_SFRUP1 0xf460
#define SFRUP1 REGx(R0900_P1_SFRUP1)
#define F0900_P1_AUTO_GUP 0xf4600080
#define AUTO_GUP FLDx(F0900_P1_AUTO_GUP)
#define F0900_P1_SYMB_FREQ_UP1 0xf460007f

/*P1_SFRUP0*/
#define R0900_P1_SFRUP0 0xf461
#define SFRUP0 REGx(R0900_P1_SFRUP0)
#define F0900_P1_SYMB_FREQ_UP0 0xf46100ff

/*P1_SFRLOW1*/
#define R0900_P1_SFRLOW1 0xf462
#define SFRLOW1 REGx(R0900_P1_SFRLOW1)
#define F0900_P1_AUTO_GLOW 0xf4620080
#define AUTO_GLOW FLDx(F0900_P1_AUTO_GLOW)
#define F0900_P1_SYMB_FREQ_LOW1 0xf462007f

/*P1_SFRLOW0*/
#define R0900_P1_SFRLOW0 0xf463
#define SFRLOW0 REGx(R0900_P1_SFRLOW0)
#define F0900_P1_SYMB_FREQ_LOW0 0xf46300ff

/*P1_SFR3*/
#define R0900_P1_SFR3 0xf464
#define SFR3 REGx(R0900_P1_SFR3)
#define F0900_P1_SYMB_FREQ3 0xf46400ff
#define SYMB_FREQ3 FLDx(F0900_P1_SYMB_FREQ3)

/*P1_SFR2*/
#define R0900_P1_SFR2 0xf465
#define SFR2 REGx(R0900_P1_SFR2)
#define F0900_P1_SYMB_FREQ2 0xf46500ff
#define SYMB_FREQ2 FLDx(F0900_P1_SYMB_FREQ2)

/*P1_SFR1*/
#define R0900_P1_SFR1 0xf466
#define SFR1 REGx(R0900_P1_SFR1)
#define F0900_P1_SYMB_FREQ1 0xf46600ff
#define SYMB_FREQ1 FLDx(F0900_P1_SYMB_FREQ1)

/*P1_SFR0*/
#define R0900_P1_SFR0 0xf467
#define SFR0 REGx(R0900_P1_SFR0)
#define F0900_P1_SYMB_FREQ0 0xf46700ff
#define SYMB_FREQ0 FLDx(F0900_P1_SYMB_FREQ0)

/*P1_TMGREG2*/
#define R0900_P1_TMGREG2 0xf468
#define TMGREG2 REGx(R0900_P1_TMGREG2)
#define F0900_P1_TMGREG2 0xf46800ff

/*P1_TMGREG1*/
#define R0900_P1_TMGREG1 0xf469
#define TMGREG1 REGx(R0900_P1_TMGREG1)
#define F0900_P1_TMGREG1 0xf46900ff

/*P1_TMGREG0*/
#define R0900_P1_TMGREG0 0xf46a
#define TMGREG0 REGx(R0900_P1_TMGREG0)
#define F0900_P1_TMGREG0 0xf46a00ff

/*P1_TMGLOCK1*/
#define R0900_P1_TMGLOCK1 0xf46b
#define TMGLOCK1 REGx(R0900_P1_TMGLOCK1)
#define F0900_P1_TMGLOCK_LEVEL1 0xf46b01ff

/*P1_TMGLOCK0*/
#define R0900_P1_TMGLOCK0 0xf46c
#define TMGLOCK0 REGx(R0900_P1_TMGLOCK0)
#define F0900_P1_TMGLOCK_LEVEL0 0xf46c00ff

/*P1_TMGOBS*/
#define R0900_P1_TMGOBS 0xf46d
#define TMGOBS REGx(R0900_P1_TMGOBS)
#define F0900_P1_ROLLOFF_STATUS 0xf46d00c0
#define ROLLOFF_STATUS FLDx(F0900_P1_ROLLOFF_STATUS)

/*P1_EQUALCFG*/
#define R0900_P1_EQUALCFG 0xf46f
#define EQUALCFG REGx(R0900_P1_EQUALCFG)
#define F0900_P1_EQUAL_ON 0xf46f0040
#define F0900_P1_MU_EQUALDFE 0xf46f0007

/*P1_EQUAI1*/
#define R0900_P1_EQUAI1 0xf470
#define EQUAI1 REGx(R0900_P1_EQUAI1)
#define F0900_P1_EQUA_ACCI1 0xf47001ff

/*P1_EQUAQ1*/
#define R0900_P1_EQUAQ1 0xf471
#define EQUAQ1 REGx(R0900_P1_EQUAQ1)
#define F0900_P1_EQUA_ACCQ1 0xf47101ff

/*P1_EQUAI2*/
#define R0900_P1_EQUAI2 0xf472
#define EQUAI2 REGx(R0900_P1_EQUAI2)
#define F0900_P1_EQUA_ACCI2 0xf47201ff

/*P1_EQUAQ2*/
#define R0900_P1_EQUAQ2 0xf473
#define EQUAQ2 REGx(R0900_P1_EQUAQ2)
#define F0900_P1_EQUA_ACCQ2 0xf47301ff

/*P1_EQUAI3*/
#define R0900_P1_EQUAI3 0xf474
#define EQUAI3 REGx(R0900_P1_EQUAI3)
#define F0900_P1_EQUA_ACCI3 0xf47401ff

/*P1_EQUAQ3*/
#define R0900_P1_EQUAQ3 0xf475
#define EQUAQ3 REGx(R0900_P1_EQUAQ3)
#define F0900_P1_EQUA_ACCQ3 0xf47501ff

/*P1_EQUAI4*/
#define R0900_P1_EQUAI4 0xf476
#define EQUAI4 REGx(R0900_P1_EQUAI4)
#define F0900_P1_EQUA_ACCI4 0xf47601ff

/*P1_EQUAQ4*/
#define R0900_P1_EQUAQ4 0xf477
#define EQUAQ4 REGx(R0900_P1_EQUAQ4)
#define F0900_P1_EQUA_ACCQ4 0xf47701ff

/*P1_EQUAI5*/
#define R0900_P1_EQUAI5 0xf478
#define EQUAI5 REGx(R0900_P1_EQUAI5)
#define F0900_P1_EQUA_ACCI5 0xf47801ff

/*P1_EQUAQ5*/
#define R0900_P1_EQUAQ5 0xf479
#define EQUAQ5 REGx(R0900_P1_EQUAQ5)
#define F0900_P1_EQUA_ACCQ5 0xf47901ff

/*P1_EQUAI6*/
#define R0900_P1_EQUAI6 0xf47a
#define EQUAI6 REGx(R0900_P1_EQUAI6)
#define F0900_P1_EQUA_ACCI6 0xf47a01ff

/*P1_EQUAQ6*/
#define R0900_P1_EQUAQ6 0xf47b
#define EQUAQ6 REGx(R0900_P1_EQUAQ6)
#define F0900_P1_EQUA_ACCQ6 0xf47b01ff

/*P1_EQUAI7*/
#define R0900_P1_EQUAI7 0xf47c
#define EQUAI7 REGx(R0900_P1_EQUAI7)
#define F0900_P1_EQUA_ACCI7 0xf47c01ff

/*P1_EQUAQ7*/
#define R0900_P1_EQUAQ7 0xf47d
#define EQUAQ7 REGx(R0900_P1_EQUAQ7)
#define F0900_P1_EQUA_ACCQ7 0xf47d01ff

/*P1_EQUAI8*/
#define R0900_P1_EQUAI8 0xf47e
#define EQUAI8 REGx(R0900_P1_EQUAI8)
#define F0900_P1_EQUA_ACCI8 0xf47e01ff

/*P1_EQUAQ8*/
#define R0900_P1_EQUAQ8 0xf47f
#define EQUAQ8 REGx(R0900_P1_EQUAQ8)
#define F0900_P1_EQUA_ACCQ8 0xf47f01ff

/*P1_NNOSDATAT1*/
#define R0900_P1_NNOSDATAT1 0xf480
#define NNOSDATAT1 REGx(R0900_P1_NNOSDATAT1)
#define F0900_P1_NOSDATAT_NORMED1 0xf48000ff
#define NOSDATAT_NORMED1 FLDx(F0900_P1_NOSDATAT_NORMED1)

/*P1_NNOSDATAT0*/
#define R0900_P1_NNOSDATAT0 0xf481
#define NNOSDATAT0 REGx(R0900_P1_NNOSDATAT0)
#define F0900_P1_NOSDATAT_NORMED0 0xf48100ff
#define NOSDATAT_NORMED0 FLDx(F0900_P1_NOSDATAT_NORMED0)

/*P1_NNOSDATA1*/
#define R0900_P1_NNOSDATA1 0xf482
#define NNOSDATA1 REGx(R0900_P1_NNOSDATA1)
#define F0900_P1_NOSDATA_NORMED1 0xf48200ff

/*P1_NNOSDATA0*/
#define R0900_P1_NNOSDATA0 0xf483
#define NNOSDATA0 REGx(R0900_P1_NNOSDATA0)
#define F0900_P1_NOSDATA_NORMED0 0xf48300ff

/*P1_NNOSPLHT1*/
#define R0900_P1_NNOSPLHT1 0xf484
#define NNOSPLHT1 REGx(R0900_P1_NNOSPLHT1)
#define F0900_P1_NOSPLHT_NORMED1 0xf48400ff
#define NOSPLHT_NORMED1 FLDx(F0900_P1_NOSPLHT_NORMED1)

/*P1_NNOSPLHT0*/
#define R0900_P1_NNOSPLHT0 0xf485
#define NNOSPLHT0 REGx(R0900_P1_NNOSPLHT0)
#define F0900_P1_NOSPLHT_NORMED0 0xf48500ff
#define NOSPLHT_NORMED0 FLDx(F0900_P1_NOSPLHT_NORMED0)

/*P1_NNOSPLH1*/
#define R0900_P1_NNOSPLH1 0xf486
#define NNOSPLH1 REGx(R0900_P1_NNOSPLH1)
#define F0900_P1_NOSPLH_NORMED1 0xf48600ff

/*P1_NNOSPLH0*/
#define R0900_P1_NNOSPLH0 0xf487
#define NNOSPLH0 REGx(R0900_P1_NNOSPLH0)
#define F0900_P1_NOSPLH_NORMED0 0xf48700ff

/*P1_NOSDATAT1*/
#define R0900_P1_NOSDATAT1 0xf488
#define NOSDATAT1 REGx(R0900_P1_NOSDATAT1)
#define F0900_P1_NOSDATAT_UNNORMED1 0xf48800ff

/*P1_NOSDATAT0*/
#define R0900_P1_NOSDATAT0 0xf489
#define NOSDATAT0 REGx(R0900_P1_NOSDATAT0)
#define F0900_P1_NOSDATAT_UNNORMED0 0xf48900ff

/*P1_NOSDATA1*/
#define R0900_P1_NOSDATA1 0xf48a
#define NOSDATA1 REGx(R0900_P1_NOSDATA1)
#define F0900_P1_NOSDATA_UNNORMED1 0xf48a00ff

/*P1_NOSDATA0*/
#define R0900_P1_NOSDATA0 0xf48b
#define NOSDATA0 REGx(R0900_P1_NOSDATA0)
#define F0900_P1_NOSDATA_UNNORMED0 0xf48b00ff

/*P1_NOSPLHT1*/
#define R0900_P1_NOSPLHT1 0xf48c
#define NOSPLHT1 REGx(R0900_P1_NOSPLHT1)
#define F0900_P1_NOSPLHT_UNNORMED1 0xf48c00ff

/*P1_NOSPLHT0*/
#define R0900_P1_NOSPLHT0 0xf48d
#define NOSPLHT0 REGx(R0900_P1_NOSPLHT0)
#define F0900_P1_NOSPLHT_UNNORMED0 0xf48d00ff

/*P1_NOSPLH1*/
#define R0900_P1_NOSPLH1 0xf48e
#define NOSPLH1 REGx(R0900_P1_NOSPLH1)
#define F0900_P1_NOSPLH_UNNORMED1 0xf48e00ff

/*P1_NOSPLH0*/
#define R0900_P1_NOSPLH0 0xf48f
#define NOSPLH0 REGx(R0900_P1_NOSPLH0)
#define F0900_P1_NOSPLH_UNNORMED0 0xf48f00ff

/*P1_CAR2CFG*/
#define R0900_P1_CAR2CFG 0xf490
#define CAR2CFG REGx(R0900_P1_CAR2CFG)
#define F0900_P1_CARRIER3_DISABLE 0xf4900040
#define F0900_P1_ROTA2ON 0xf4900004
#define F0900_P1_PH_DET_ALGO2 0xf4900003

/*P1_CFR2CFR1*/
#define R0900_P1_CFR2CFR1 0xf491
#define CFR2CFR1 REGx(R0900_P1_CFR2CFR1)
#define F0900_P1_CFR2TOCFR1_DVBS1 0xf49100c0
#define F0900_P1_EN_S2CAR2CENTER 0xf4910020
#define F0900_P1_DIS_BCHERRCFR2 0xf4910010
#define F0900_P1_CFR2TOCFR1_BETA 0xf4910007

/*P1_CFR22*/
#define R0900_P1_CFR22 0xf493
#define CFR22 REGx(R0900_P1_CFR22)
#define F0900_P1_CAR2_FREQ2 0xf49301ff

/*P1_CFR21*/
#define R0900_P1_CFR21 0xf494
#define CFR21 REGx(R0900_P1_CFR21)
#define F0900_P1_CAR2_FREQ1 0xf49400ff

/*P1_CFR20*/
#define R0900_P1_CFR20 0xf495
#define CFR20 REGx(R0900_P1_CFR20)
#define F0900_P1_CAR2_FREQ0 0xf49500ff

/*P1_ACLC2S2Q*/
#define R0900_P1_ACLC2S2Q 0xf497
#define ACLC2S2Q REGx(R0900_P1_ACLC2S2Q)
#define F0900_P1_ENAB_SPSKSYMB 0xf4970080
#define F0900_P1_CAR2S2_Q_ALPH_M 0xf4970030
#define F0900_P1_CAR2S2_Q_ALPH_E 0xf497000f

/*P1_ACLC2S28*/
#define R0900_P1_ACLC2S28 0xf498
#define ACLC2S28 REGx(R0900_P1_ACLC2S28)
#define F0900_P1_OLDI3Q_MODE 0xf4980080
#define F0900_P1_CAR2S2_8_ALPH_M 0xf4980030
#define F0900_P1_CAR2S2_8_ALPH_E 0xf498000f

/*P1_ACLC2S216A*/
#define R0900_P1_ACLC2S216A 0xf499
#define ACLC2S216A REGx(R0900_P1_ACLC2S216A)
#define F0900_P1_DIS_C3STOPA2 0xf4990080
#define F0900_P1_CAR2S2_16ADERAT 0xf4990040
#define F0900_P1_CAR2S2_16A_ALPH_M 0xf4990030
#define F0900_P1_CAR2S2_16A_ALPH_E 0xf499000f

/*P1_ACLC2S232A*/
#define R0900_P1_ACLC2S232A 0xf49a
#define ACLC2S232A REGx(R0900_P1_ACLC2S232A)
#define F0900_P1_CAR2S2_32ADERAT 0xf49a0040
#define F0900_P1_CAR2S2_32A_ALPH_M 0xf49a0030
#define F0900_P1_CAR2S2_32A_ALPH_E 0xf49a000f

/*P1_BCLC2S2Q*/
#define R0900_P1_BCLC2S2Q 0xf49c
#define BCLC2S2Q REGx(R0900_P1_BCLC2S2Q)
#define F0900_P1_CAR2S2_Q_BETA_M 0xf49c0030
#define F0900_P1_CAR2S2_Q_BETA_E 0xf49c000f

/*P1_BCLC2S28*/
#define R0900_P1_BCLC2S28 0xf49d
#define BCLC2S28 REGx(R0900_P1_BCLC2S28)
#define F0900_P1_CAR2S2_8_BETA_M 0xf49d0030
#define F0900_P1_CAR2S2_8_BETA_E 0xf49d000f

/*P1_BCLC2S216A*/
#define R0900_P1_BCLC2S216A 0xf49e
#define BCLC2S216A REGx(R0900_P1_BCLC2S216A)

/*P1_BCLC2S232A*/
#define R0900_P1_BCLC2S232A 0xf49f
#define BCLC2S232A REGx(R0900_P1_BCLC2S232A)

/*P1_PLROOT2*/
#define R0900_P1_PLROOT2 0xf4ac
#define PLROOT2 REGx(R0900_P1_PLROOT2)
#define F0900_P1_PLSCRAMB_MODE 0xf4ac000c
#define F0900_P1_PLSCRAMB_ROOT2 0xf4ac0003

/*P1_PLROOT1*/
#define R0900_P1_PLROOT1 0xf4ad
#define PLROOT1 REGx(R0900_P1_PLROOT1)
#define F0900_P1_PLSCRAMB_ROOT1 0xf4ad00ff

/*P1_PLROOT0*/
#define R0900_P1_PLROOT0 0xf4ae
#define PLROOT0 REGx(R0900_P1_PLROOT0)
#define F0900_P1_PLSCRAMB_ROOT0 0xf4ae00ff

/*P1_MODCODLST0*/
#define R0900_P1_MODCODLST0 0xf4b0
#define MODCODLST0 REGx(R0900_P1_MODCODLST0)

/*P1_MODCODLST1*/
#define R0900_P1_MODCODLST1 0xf4b1
#define MODCODLST1 REGx(R0900_P1_MODCODLST1)
#define F0900_P1_DIS_MODCOD29 0xf4b100f0
#define F0900_P1_DIS_32PSK_9_10 0xf4b1000f

/*P1_MODCODLST2*/
#define R0900_P1_MODCODLST2 0xf4b2
#define MODCODLST2 REGx(R0900_P1_MODCODLST2)
#define F0900_P1_DIS_32PSK_8_9 0xf4b200f0
#define F0900_P1_DIS_32PSK_5_6 0xf4b2000f

/*P1_MODCODLST3*/
#define R0900_P1_MODCODLST3 0xf4b3
#define MODCODLST3 REGx(R0900_P1_MODCODLST3)
#define F0900_P1_DIS_32PSK_4_5 0xf4b300f0
#define F0900_P1_DIS_32PSK_3_4 0xf4b3000f

/*P1_MODCODLST4*/
#define R0900_P1_MODCODLST4 0xf4b4
#define MODCODLST4 REGx(R0900_P1_MODCODLST4)
#define F0900_P1_DIS_16PSK_9_10 0xf4b400f0
#define F0900_P1_DIS_16PSK_8_9 0xf4b4000f

/*P1_MODCODLST5*/
#define R0900_P1_MODCODLST5 0xf4b5
#define MODCODLST5 REGx(R0900_P1_MODCODLST5)
#define F0900_P1_DIS_16PSK_5_6 0xf4b500f0
#define F0900_P1_DIS_16PSK_4_5 0xf4b5000f

/*P1_MODCODLST6*/
#define R0900_P1_MODCODLST6 0xf4b6
#define MODCODLST6 REGx(R0900_P1_MODCODLST6)
#define F0900_P1_DIS_16PSK_3_4 0xf4b600f0
#define F0900_P1_DIS_16PSK_2_3 0xf4b6000f

/*P1_MODCODLST7*/
#define R0900_P1_MODCODLST7 0xf4b7
#define MODCODLST7 REGx(R0900_P1_MODCODLST7)
#define F0900_P1_DIS_8P_9_10 0xf4b700f0
#define F0900_P1_DIS_8P_8_9 0xf4b7000f

/*P1_MODCODLST8*/
#define R0900_P1_MODCODLST8 0xf4b8
#define MODCODLST8 REGx(R0900_P1_MODCODLST8)
#define F0900_P1_DIS_8P_5_6 0xf4b800f0
#define F0900_P1_DIS_8P_3_4 0xf4b8000f

/*P1_MODCODLST9*/
#define R0900_P1_MODCODLST9 0xf4b9
#define MODCODLST9 REGx(R0900_P1_MODCODLST9)
#define F0900_P1_DIS_8P_2_3 0xf4b900f0
#define F0900_P1_DIS_8P_3_5 0xf4b9000f

/*P1_MODCODLSTA*/
#define R0900_P1_MODCODLSTA 0xf4ba
#define MODCODLSTA REGx(R0900_P1_MODCODLSTA)
#define F0900_P1_DIS_QP_9_10 0xf4ba00f0
#define F0900_P1_DIS_QP_8_9 0xf4ba000f

/*P1_MODCODLSTB*/
#define R0900_P1_MODCODLSTB 0xf4bb
#define MODCODLSTB REGx(R0900_P1_MODCODLSTB)
#define F0900_P1_DIS_QP_5_6 0xf4bb00f0
#define F0900_P1_DIS_QP_4_5 0xf4bb000f

/*P1_MODCODLSTC*/
#define R0900_P1_MODCODLSTC 0xf4bc
#define MODCODLSTC REGx(R0900_P1_MODCODLSTC)
#define F0900_P1_DIS_QP_3_4 0xf4bc00f0
#define F0900_P1_DIS_QP_2_3 0xf4bc000f

/*P1_MODCODLSTD*/
#define R0900_P1_MODCODLSTD 0xf4bd
#define MODCODLSTD REGx(R0900_P1_MODCODLSTD)
#define F0900_P1_DIS_QP_3_5 0xf4bd00f0
#define F0900_P1_DIS_QP_1_2 0xf4bd000f

/*P1_MODCODLSTE*/
#define R0900_P1_MODCODLSTE 0xf4be
#define MODCODLSTE REGx(R0900_P1_MODCODLSTE)
#define F0900_P1_DIS_QP_2_5 0xf4be00f0
#define F0900_P1_DIS_QP_1_3 0xf4be000f

/*P1_MODCODLSTF*/
#define R0900_P1_MODCODLSTF 0xf4bf
#define MODCODLSTF REGx(R0900_P1_MODCODLSTF)
#define F0900_P1_DIS_QP_1_4 0xf4bf00f0

/*P1_GAUSSR0*/
#define R0900_P1_GAUSSR0 0xf4c0
#define GAUSSR0 REGx(R0900_P1_GAUSSR0)
#define F0900_P1_EN_CCIMODE 0xf4c00080
#define F0900_P1_R0_GAUSSIEN 0xf4c0007f

/*P1_CCIR0*/
#define R0900_P1_CCIR0 0xf4c1
#define CCIR0 REGx(R0900_P1_CCIR0)
#define F0900_P1_CCIDETECT_PLHONLY 0xf4c10080
#define F0900_P1_R0_CCI 0xf4c1007f

/*P1_CCIQUANT*/
#define R0900_P1_CCIQUANT 0xf4c2
#define CCIQUANT REGx(R0900_P1_CCIQUANT)
#define F0900_P1_CCI_BETA 0xf4c200e0
#define F0900_P1_CCI_QUANT 0xf4c2001f

/*P1_CCITHRES*/
#define R0900_P1_CCITHRES 0xf4c3
#define CCITHRES REGx(R0900_P1_CCITHRES)
#define F0900_P1_CCI_THRESHOLD 0xf4c300ff

/*P1_CCIACC*/
#define R0900_P1_CCIACC 0xf4c4
#define CCIACC REGx(R0900_P1_CCIACC)
#define F0900_P1_CCI_VALUE 0xf4c400ff

/*P1_DMDRESCFG*/
#define R0900_P1_DMDRESCFG 0xf4c6
#define DMDRESCFG REGx(R0900_P1_DMDRESCFG)
#define F0900_P1_DMDRES_RESET 0xf4c60080
#define F0900_P1_DMDRES_STRALL 0xf4c60008
#define F0900_P1_DMDRES_NEWONLY 0xf4c60004
#define F0900_P1_DMDRES_NOSTORE 0xf4c60002

/*P1_DMDRESADR*/
#define R0900_P1_DMDRESADR 0xf4c7
#define DMDRESADR REGx(R0900_P1_DMDRESADR)
#define F0900_P1_DMDRES_VALIDCFR 0xf4c70040
#define F0900_P1_DMDRES_MEMFULL 0xf4c70030
#define F0900_P1_DMDRES_RESNBR 0xf4c7000f

/*P1_DMDRESDATA7*/
#define R0900_P1_DMDRESDATA7 0xf4c8
#define F0900_P1_DMDRES_DATA7 0xf4c800ff

/*P1_DMDRESDATA6*/
#define R0900_P1_DMDRESDATA6 0xf4c9
#define F0900_P1_DMDRES_DATA6 0xf4c900ff

/*P1_DMDRESDATA5*/
#define R0900_P1_DMDRESDATA5 0xf4ca
#define F0900_P1_DMDRES_DATA5 0xf4ca00ff

/*P1_DMDRESDATA4*/
#define R0900_P1_DMDRESDATA4 0xf4cb
#define F0900_P1_DMDRES_DATA4 0xf4cb00ff

/*P1_DMDRESDATA3*/
#define R0900_P1_DMDRESDATA3 0xf4cc
#define F0900_P1_DMDRES_DATA3 0xf4cc00ff

/*P1_DMDRESDATA2*/
#define R0900_P1_DMDRESDATA2 0xf4cd
#define F0900_P1_DMDRES_DATA2 0xf4cd00ff

/*P1_DMDRESDATA1*/
#define R0900_P1_DMDRESDATA1 0xf4ce
#define F0900_P1_DMDRES_DATA1 0xf4ce00ff

/*P1_DMDRESDATA0*/
#define R0900_P1_DMDRESDATA0 0xf4cf
#define F0900_P1_DMDRES_DATA0 0xf4cf00ff

/*P1_FFEI1*/
#define R0900_P1_FFEI1 0xf4d0
#define FFEI1 REGx(R0900_P1_FFEI1)
#define F0900_P1_FFE_ACCI1 0xf4d001ff

/*P1_FFEQ1*/
#define R0900_P1_FFEQ1 0xf4d1
#define FFEQ1 REGx(R0900_P1_FFEQ1)
#define F0900_P1_FFE_ACCQ1 0xf4d101ff

/*P1_FFEI2*/
#define R0900_P1_FFEI2 0xf4d2
#define FFEI2 REGx(R0900_P1_FFEI2)
#define F0900_P1_FFE_ACCI2 0xf4d201ff

/*P1_FFEQ2*/
#define R0900_P1_FFEQ2 0xf4d3
#define FFEQ2 REGx(R0900_P1_FFEQ2)
#define F0900_P1_FFE_ACCQ2 0xf4d301ff

/*P1_FFEI3*/
#define R0900_P1_FFEI3 0xf4d4
#define FFEI3 REGx(R0900_P1_FFEI3)
#define F0900_P1_FFE_ACCI3 0xf4d401ff

/*P1_FFEQ3*/
#define R0900_P1_FFEQ3 0xf4d5
#define FFEQ3 REGx(R0900_P1_FFEQ3)
#define F0900_P1_FFE_ACCQ3 0xf4d501ff

/*P1_FFEI4*/
#define R0900_P1_FFEI4 0xf4d6
#define FFEI4 REGx(R0900_P1_FFEI4)
#define F0900_P1_FFE_ACCI4 0xf4d601ff

/*P1_FFEQ4*/
#define R0900_P1_FFEQ4 0xf4d7
#define FFEQ4 REGx(R0900_P1_FFEQ4)
#define F0900_P1_FFE_ACCQ4 0xf4d701ff

/*P1_FFECFG*/
#define R0900_P1_FFECFG 0xf4d8
#define FFECFG REGx(R0900_P1_FFECFG)
#define F0900_P1_EQUALFFE_ON 0xf4d80040
#define F0900_P1_MU_EQUALFFE 0xf4d80007

/*P1_TNRCFG*/
#define R0900_P1_TNRCFG 0xf4e0
#define TNRCFG REGx(R0900_P1_TNRCFG)
#define F0900_P1_TUN_ACKFAIL 0xf4e00080
#define F0900_P1_TUN_TYPE 0xf4e00070
#define F0900_P1_TUN_SECSTOP 0xf4e00008
#define F0900_P1_TUN_VCOSRCH 0xf4e00004
#define F0900_P1_TUN_MADDRESS 0xf4e00003

/*P1_TNRCFG2*/
#define R0900_P1_TNRCFG2 0xf4e1
#define TNRCFG2 REGx(R0900_P1_TNRCFG2)
#define F0900_P1_TUN_IQSWAP 0xf4e10080
#define F0900_P1_DIS_BWCALC 0xf4e10004
#define F0900_P1_SHORT_WAITSTATES 0xf4e10002

/*P1_TNRXTAL*/
#define R0900_P1_TNRXTAL 0xf4e4
#define TNRXTAL REGx(R0900_P1_TNRXTAL)
#define F0900_P1_TUN_XTALFREQ 0xf4e4001f

/*P1_TNRSTEPS*/
#define R0900_P1_TNRSTEPS 0xf4e7
#define TNRSTEPS REGx(R0900_P1_TNRSTEPS)
#define F0900_P1_TUNER_BW0P125 0xf4e70080
#define F0900_P1_BWINC_OFFSET 0xf4e70170
#define F0900_P1_SOFTSTEP_RNG 0xf4e70008
#define F0900_P1_TUN_BWOFFSET 0xf4e70007

/*P1_TNRGAIN*/
#define R0900_P1_TNRGAIN 0xf4e8
#define TNRGAIN REGx(R0900_P1_TNRGAIN)
#define F0900_P1_TUN_KDIVEN 0xf4e800c0
#define F0900_P1_STB6X00_OCK 0xf4e80030
#define F0900_P1_TUN_GAIN 0xf4e8000f

/*P1_TNRRF1*/
#define R0900_P1_TNRRF1 0xf4e9
#define TNRRF1 REGx(R0900_P1_TNRRF1)
#define F0900_P1_TUN_RFFREQ2 0xf4e900ff
#define TUN_RFFREQ2 FLDx(F0900_P1_TUN_RFFREQ2)

/*P1_TNRRF0*/
#define R0900_P1_TNRRF0 0xf4ea
#define TNRRF0 REGx(R0900_P1_TNRRF0)
#define F0900_P1_TUN_RFFREQ1 0xf4ea00ff
#define TUN_RFFREQ1 FLDx(F0900_P1_TUN_RFFREQ1)

/*P1_TNRBW*/
#define R0900_P1_TNRBW 0xf4eb
#define TNRBW REGx(R0900_P1_TNRBW)
#define F0900_P1_TUN_RFFREQ0 0xf4eb00c0
#define TUN_RFFREQ0 FLDx(F0900_P1_TUN_RFFREQ0)
#define F0900_P1_TUN_BW 0xf4eb003f
#define TUN_BW FLDx(F0900_P1_TUN_BW)

/*P1_TNRADJ*/
#define R0900_P1_TNRADJ 0xf4ec
#define TNRADJ REGx(R0900_P1_TNRADJ)
#define F0900_P1_STB61X0_CALTIME 0xf4ec0040

/*P1_TNRCTL2*/
#define R0900_P1_TNRCTL2 0xf4ed
#define TNRCTL2 REGx(R0900_P1_TNRCTL2)
#define F0900_P1_STB61X0_RCCKOFF 0xf4ed0080
#define F0900_P1_STB61X0_ICP_SDOFF 0xf4ed0040
#define F0900_P1_STB61X0_DCLOOPOFF 0xf4ed0020
#define F0900_P1_STB61X0_REFOUTSEL 0xf4ed0010
#define F0900_P1_STB61X0_CALOFF 0xf4ed0008
#define F0900_P1_STB6XX0_LPT_BEN 0xf4ed0004
#define F0900_P1_STB6XX0_RX_OSCP 0xf4ed0002
#define F0900_P1_STB6XX0_SYN 0xf4ed0001

/*P1_TNRCFG3*/
#define R0900_P1_TNRCFG3 0xf4ee
#define TNRCFG3 REGx(R0900_P1_TNRCFG3)
#define F0900_P1_TUN_PLLFREQ 0xf4ee001c
#define F0900_P1_TUN_I2CFREQ_MODE 0xf4ee0003

/*P1_TNRLAUNCH*/
#define R0900_P1_TNRLAUNCH 0xf4f0
#define TNRLAUNCH REGx(R0900_P1_TNRLAUNCH)

/*P1_TNRLD*/
#define R0900_P1_TNRLD 0xf4f0
#define TNRLD REGx(R0900_P1_TNRLD)
#define F0900_P1_TUNLD_VCOING 0xf4f00080
#define F0900_P1_TUN_REG1FAIL 0xf4f00040
#define F0900_P1_TUN_REG2FAIL 0xf4f00020
#define F0900_P1_TUN_REG3FAIL 0xf4f00010
#define F0900_P1_TUN_REG4FAIL 0xf4f00008
#define F0900_P1_TUN_REG5FAIL 0xf4f00004
#define F0900_P1_TUN_BWING 0xf4f00002
#define F0900_P1_TUN_LOCKED 0xf4f00001

/*P1_TNROBSL*/
#define R0900_P1_TNROBSL 0xf4f6
#define TNROBSL REGx(R0900_P1_TNROBSL)
#define F0900_P1_TUN_I2CABORTED 0xf4f60080
#define F0900_P1_TUN_LPEN 0xf4f60040
#define F0900_P1_TUN_FCCK 0xf4f60020
#define F0900_P1_TUN_I2CLOCKED 0xf4f60010
#define F0900_P1_TUN_PROGDONE 0xf4f6000c
#define F0900_P1_TUN_RFRESTE1 0xf4f60003
#define TUN_RFRESTE1 FLDx(F0900_P1_TUN_RFRESTE1)

/*P1_TNRRESTE*/
#define R0900_P1_TNRRESTE 0xf4f7
#define TNRRESTE REGx(R0900_P1_TNRRESTE)
#define F0900_P1_TUN_RFRESTE0 0xf4f700ff
#define TUN_RFRESTE0 FLDx(F0900_P1_TUN_RFRESTE0)

/*P1_SMAPCOEF7*/
#define R0900_P1_SMAPCOEF7 0xf500
#define SMAPCOEF7 REGx(R0900_P1_SMAPCOEF7)
#define F0900_P1_DIS_QSCALE 0xf5000080
#define F0900_P1_SMAPCOEF_Q_LLR12 0xf500017f

/*P1_SMAPCOEF6*/
#define R0900_P1_SMAPCOEF6 0xf501
#define SMAPCOEF6 REGx(R0900_P1_SMAPCOEF6)
#define F0900_P1_ADJ_8PSKLLR1 0xf5010004
#define F0900_P1_OLD_8PSKLLR1 0xf5010002
#define F0900_P1_DIS_AB8PSK 0xf5010001

/*P1_SMAPCOEF5*/
#define R0900_P1_SMAPCOEF5 0xf502
#define SMAPCOEF5 REGx(R0900_P1_SMAPCOEF5)
#define F0900_P1_DIS_8SCALE 0xf5020080
#define F0900_P1_SMAPCOEF_8P_LLR23 0xf502017f

/*P1_NCO2MAX1*/
#define R0900_P1_NCO2MAX1 0xf514
#define NCO2MAX1 REGx(R0900_P1_NCO2MAX1)
#define F0900_P1_TETA2_MAXVABS1 0xf51400ff

/*P1_NCO2MAX0*/
#define R0900_P1_NCO2MAX0 0xf515
#define NCO2MAX0 REGx(R0900_P1_NCO2MAX0)
#define F0900_P1_TETA2_MAXVABS0 0xf51500ff

/*P1_NCO2FR1*/
#define R0900_P1_NCO2FR1 0xf516
#define NCO2FR1 REGx(R0900_P1_NCO2FR1)
#define F0900_P1_NCO2FINAL_ANGLE1 0xf51600ff

/*P1_NCO2FR0*/
#define R0900_P1_NCO2FR0 0xf517
#define NCO2FR0 REGx(R0900_P1_NCO2FR0)
#define F0900_P1_NCO2FINAL_ANGLE0 0xf51700ff

/*P1_CFR2AVRGE1*/
#define R0900_P1_CFR2AVRGE1 0xf518
#define CFR2AVRGE1 REGx(R0900_P1_CFR2AVRGE1)
#define F0900_P1_I2C_CFR2AVERAGE1 0xf51800ff

/*P1_CFR2AVRGE0*/
#define R0900_P1_CFR2AVRGE0 0xf519
#define CFR2AVRGE0 REGx(R0900_P1_CFR2AVRGE0)
#define F0900_P1_I2C_CFR2AVERAGE0 0xf51900ff

/*P1_DMDPLHSTAT*/
#define R0900_P1_DMDPLHSTAT 0xf520
#define DMDPLHSTAT REGx(R0900_P1_DMDPLHSTAT)
#define F0900_P1_PLH_STATISTIC 0xf52000ff

/*P1_LOCKTIME3*/
#define R0900_P1_LOCKTIME3 0xf522
#define LOCKTIME3 REGx(R0900_P1_LOCKTIME3)
#define F0900_P1_DEMOD_LOCKTIME3 0xf52200ff

/*P1_LOCKTIME2*/
#define R0900_P1_LOCKTIME2 0xf523
#define LOCKTIME2 REGx(R0900_P1_LOCKTIME2)
#define F0900_P1_DEMOD_LOCKTIME2 0xf52300ff

/*P1_LOCKTIME1*/
#define R0900_P1_LOCKTIME1 0xf524
#define LOCKTIME1 REGx(R0900_P1_LOCKTIME1)
#define F0900_P1_DEMOD_LOCKTIME1 0xf52400ff

/*P1_LOCKTIME0*/
#define R0900_P1_LOCKTIME0 0xf525
#define LOCKTIME0 REGx(R0900_P1_LOCKTIME0)
#define F0900_P1_DEMOD_LOCKTIME0 0xf52500ff

/*P1_VITSCALE*/
#define R0900_P1_VITSCALE 0xf532
#define VITSCALE REGx(R0900_P1_VITSCALE)
#define F0900_P1_NVTH_NOSRANGE 0xf5320080
#define F0900_P1_VERROR_MAXMODE 0xf5320040
#define F0900_P1_NSLOWSN_LOCKED 0xf5320008
#define F0900_P1_DIS_RSFLOCK 0xf5320002

/*P1_FECM*/
#define R0900_P1_FECM 0xf533
#define FECM REGx(R0900_P1_FECM)
#define F0900_P1_DSS_DVB 0xf5330080
#define DSS_DVB FLDx(F0900_P1_DSS_DVB)
#define F0900_P1_DSS_SRCH 0xf5330010
#define F0900_P1_SYNCVIT 0xf5330002
#define F0900_P1_IQINV 0xf5330001
#define IQINV FLDx(F0900_P1_IQINV)

/*P1_VTH12*/
#define R0900_P1_VTH12 0xf534
#define VTH12 REGx(R0900_P1_VTH12)
#define F0900_P1_VTH12 0xf53400ff

/*P1_VTH23*/
#define R0900_P1_VTH23 0xf535
#define VTH23 REGx(R0900_P1_VTH23)
#define F0900_P1_VTH23 0xf53500ff

/*P1_VTH34*/
#define R0900_P1_VTH34 0xf536
#define VTH34 REGx(R0900_P1_VTH34)
#define F0900_P1_VTH34 0xf53600ff

/*P1_VTH56*/
#define R0900_P1_VTH56 0xf537
#define VTH56 REGx(R0900_P1_VTH56)
#define F0900_P1_VTH56 0xf53700ff

/*P1_VTH67*/
#define R0900_P1_VTH67 0xf538
#define VTH67 REGx(R0900_P1_VTH67)
#define F0900_P1_VTH67 0xf53800ff

/*P1_VTH78*/
#define R0900_P1_VTH78 0xf539
#define VTH78 REGx(R0900_P1_VTH78)
#define F0900_P1_VTH78 0xf53900ff

/*P1_VITCURPUN*/
#define R0900_P1_VITCURPUN 0xf53a
#define VITCURPUN REGx(R0900_P1_VITCURPUN)
#define F0900_P1_VIT_CURPUN 0xf53a001f
#define VIT_CURPUN FLDx(F0900_P1_VIT_CURPUN)

/*P1_VERROR*/
#define R0900_P1_VERROR 0xf53b
#define VERROR REGx(R0900_P1_VERROR)
#define F0900_P1_REGERR_VIT 0xf53b00ff

/*P1_PRVIT*/
#define R0900_P1_PRVIT 0xf53c
#define PRVIT REGx(R0900_P1_PRVIT)
#define F0900_P1_DIS_VTHLOCK 0xf53c0040
#define F0900_P1_E7_8VIT 0xf53c0020
#define F0900_P1_E6_7VIT 0xf53c0010
#define F0900_P1_E5_6VIT 0xf53c0008
#define F0900_P1_E3_4VIT 0xf53c0004
#define F0900_P1_E2_3VIT 0xf53c0002
#define F0900_P1_E1_2VIT 0xf53c0001

/*P1_VAVSRVIT*/
#define R0900_P1_VAVSRVIT 0xf53d
#define VAVSRVIT REGx(R0900_P1_VAVSRVIT)
#define F0900_P1_AMVIT 0xf53d0080
#define F0900_P1_FROZENVIT 0xf53d0040
#define F0900_P1_SNVIT 0xf53d0030
#define F0900_P1_TOVVIT 0xf53d000c
#define F0900_P1_HYPVIT 0xf53d0003

/*P1_VSTATUSVIT*/
#define R0900_P1_VSTATUSVIT 0xf53e
#define VSTATUSVIT REGx(R0900_P1_VSTATUSVIT)
#define F0900_P1_PRFVIT 0xf53e0010
#define PRFVIT FLDx(F0900_P1_PRFVIT)
#define F0900_P1_LOCKEDVIT 0xf53e0008
#define LOCKEDVIT FLDx(F0900_P1_LOCKEDVIT)

/*P1_VTHINUSE*/
#define R0900_P1_VTHINUSE 0xf53f
#define VTHINUSE REGx(R0900_P1_VTHINUSE)
#define F0900_P1_VIT_INUSE 0xf53f00ff

/*P1_KDIV12*/
#define R0900_P1_KDIV12 0xf540
#define KDIV12 REGx(R0900_P1_KDIV12)
#define F0900_P1_K_DIVIDER_12 0xf540007f

/*P1_KDIV23*/
#define R0900_P1_KDIV23 0xf541
#define KDIV23 REGx(R0900_P1_KDIV23)
#define F0900_P1_K_DIVIDER_23 0xf541007f

/*P1_KDIV34*/
#define R0900_P1_KDIV34 0xf542
#define KDIV34 REGx(R0900_P1_KDIV34)
#define F0900_P1_K_DIVIDER_34 0xf542007f

/*P1_KDIV56*/
#define R0900_P1_KDIV56 0xf543
#define KDIV56 REGx(R0900_P1_KDIV56)
#define F0900_P1_K_DIVIDER_56 0xf543007f

/*P1_KDIV67*/
#define R0900_P1_KDIV67 0xf544
#define KDIV67 REGx(R0900_P1_KDIV67)
#define F0900_P1_K_DIVIDER_67 0xf544007f

/*P1_KDIV78*/
#define R0900_P1_KDIV78 0xf545
#define KDIV78 REGx(R0900_P1_KDIV78)
#define F0900_P1_K_DIVIDER_78 0xf545007f

/*P1_PDELCTRL1*/
#define R0900_P1_PDELCTRL1 0xf550
#define PDELCTRL1 REGx(R0900_P1_PDELCTRL1)
#define F0900_P1_INV_MISMASK 0xf5500080
#define INV_MISMASK FLDx(F0900_P1_INV_MISMASK)
#define F0900_P1_FILTER_EN 0xf5500020
#define FILTER_EN FLDx(F0900_P1_FILTER_EN)
#define F0900_P1_EN_MIS00 0xf5500002
#define EN_MIS00 FLDx(F0900_P1_EN_MIS00)
#define F0900_P1_ALGOSWRST 0xf5500001
#define ALGOSWRST FLDx(F0900_P1_ALGOSWRST)

/*P1_PDELCTRL2*/
#define R0900_P1_PDELCTRL2 0xf551
#define PDELCTRL2 REGx(R0900_P1_PDELCTRL2)
#define F0900_P1_RESET_UPKO_COUNT 0xf5510040
#define RESET_UPKO_COUNT FLDx(F0900_P1_RESET_UPKO_COUNT)
#define F0900_P1_FRAME_MODE 0xf5510002
#define F0900_P1_NOBCHERRFLG_USE 0xf5510001

/*P1_HYSTTHRESH*/
#define R0900_P1_HYSTTHRESH 0xf554
#define HYSTTHRESH REGx(R0900_P1_HYSTTHRESH)
#define F0900_P1_UNLCK_THRESH 0xf55400f0
#define F0900_P1_DELIN_LCK_THRESH 0xf554000f

/*P1_ISIENTRY*/
#define R0900_P1_ISIENTRY 0xf55e
#define ISIENTRY REGx(R0900_P1_ISIENTRY)
#define F0900_P1_ISI_ENTRY 0xf55e00ff

/*P1_ISIBITENA*/
#define R0900_P1_ISIBITENA 0xf55f
#define ISIBITENA REGx(R0900_P1_ISIBITENA)
#define F0900_P1_ISI_BIT_EN 0xf55f00ff

/*P1_MATSTR1*/
#define R0900_P1_MATSTR1 0xf560
#define MATSTR1 REGx(R0900_P1_MATSTR1)
#define F0900_P1_MATYPE_CURRENT1 0xf56000ff

/*P1_MATSTR0*/
#define R0900_P1_MATSTR0 0xf561
#define MATSTR0 REGx(R0900_P1_MATSTR0)
#define F0900_P1_MATYPE_CURRENT0 0xf56100ff

/*P1_UPLSTR1*/
#define R0900_P1_UPLSTR1 0xf562
#define UPLSTR1 REGx(R0900_P1_UPLSTR1)
#define F0900_P1_UPL_CURRENT1 0xf56200ff

/*P1_UPLSTR0*/
#define R0900_P1_UPLSTR0 0xf563
#define UPLSTR0 REGx(R0900_P1_UPLSTR0)
#define F0900_P1_UPL_CURRENT0 0xf56300ff

/*P1_DFLSTR1*/
#define R0900_P1_DFLSTR1 0xf564
#define DFLSTR1 REGx(R0900_P1_DFLSTR1)
#define F0900_P1_DFL_CURRENT1 0xf56400ff

/*P1_DFLSTR0*/
#define R0900_P1_DFLSTR0 0xf565
#define DFLSTR0 REGx(R0900_P1_DFLSTR0)
#define F0900_P1_DFL_CURRENT0 0xf56500ff

/*P1_SYNCSTR*/
#define R0900_P1_SYNCSTR 0xf566
#define SYNCSTR REGx(R0900_P1_SYNCSTR)
#define F0900_P1_SYNC_CURRENT 0xf56600ff

/*P1_SYNCDSTR1*/
#define R0900_P1_SYNCDSTR1 0xf567
#define SYNCDSTR1 REGx(R0900_P1_SYNCDSTR1)
#define F0900_P1_SYNCD_CURRENT1 0xf56700ff

/*P1_SYNCDSTR0*/
#define R0900_P1_SYNCDSTR0 0xf568
#define SYNCDSTR0 REGx(R0900_P1_SYNCDSTR0)
#define F0900_P1_SYNCD_CURRENT0 0xf56800ff

/*P1_PDELSTATUS1*/
#define R0900_P1_PDELSTATUS1 0xf569
#define F0900_P1_PKTDELIN_DELOCK 0xf5690080
#define F0900_P1_SYNCDUPDFL_BADDFL 0xf5690040
#define F0900_P1_CONTINUOUS_STREAM 0xf5690020
#define F0900_P1_UNACCEPTED_STREAM 0xf5690010
#define F0900_P1_BCH_ERROR_FLAG 0xf5690008
#define F0900_P1_PKTDELIN_LOCK 0xf5690002
#define PKTDELIN_LOCK FLDx(F0900_P1_PKTDELIN_LOCK)
#define F0900_P1_FIRST_LOCK 0xf5690001

/*P1_PDELSTATUS2*/
#define R0900_P1_PDELSTATUS2 0xf56a
#define F0900_P1_FRAME_MODCOD 0xf56a007c
#define F0900_P1_FRAME_TYPE 0xf56a0003

/*P1_BBFCRCKO1*/
#define R0900_P1_BBFCRCKO1 0xf56b
#define BBFCRCKO1 REGx(R0900_P1_BBFCRCKO1)
#define F0900_P1_BBHCRC_KOCNT1 0xf56b00ff

/*P1_BBFCRCKO0*/
#define R0900_P1_BBFCRCKO0 0xf56c
#define BBFCRCKO0 REGx(R0900_P1_BBFCRCKO0)
#define F0900_P1_BBHCRC_KOCNT0 0xf56c00ff

/*P1_UPCRCKO1*/
#define R0900_P1_UPCRCKO1 0xf56d
#define UPCRCKO1 REGx(R0900_P1_UPCRCKO1)
#define F0900_P1_PKTCRC_KOCNT1 0xf56d00ff

/*P1_UPCRCKO0*/
#define R0900_P1_UPCRCKO0 0xf56e
#define UPCRCKO0 REGx(R0900_P1_UPCRCKO0)
#define F0900_P1_PKTCRC_KOCNT0 0xf56e00ff

/*P1_PDELCTRL3*/
#define R0900_P1_PDELCTRL3 0xf56f
#define PDELCTRL3 REGx(R0900_P1_PDELCTRL3)
#define F0900_P1_PKTDEL_CONTFAIL 0xf56f0080
#define F0900_P1_NOFIFO_BCHERR 0xf56f0020

/*P1_TSSTATEM*/
#define R0900_P1_TSSTATEM 0xf570
#define TSSTATEM REGx(R0900_P1_TSSTATEM)
#define F0900_P1_TSDIL_ON 0xf5700080
#define F0900_P1_TSRS_ON 0xf5700020
#define F0900_P1_TSDESCRAMB_ON 0xf5700010
#define F0900_P1_TSFRAME_MODE 0xf5700008
#define F0900_P1_TS_DISABLE 0xf5700004
#define F0900_P1_TSOUT_NOSYNC 0xf5700001

/*P1_TSCFGH*/
#define R0900_P1_TSCFGH 0xf572
#define TSCFGH REGx(R0900_P1_TSCFGH)
#define F0900_P1_TSFIFO_DVBCI 0xf5720080
#define F0900_P1_TSFIFO_SERIAL 0xf5720040
#define F0900_P1_TSFIFO_TEIUPDATE 0xf5720020
#define F0900_P1_TSFIFO_DUTY50 0xf5720010
#define F0900_P1_TSFIFO_HSGNLOUT 0xf5720008
#define F0900_P1_TSFIFO_ERRMODE 0xf5720006
#define F0900_P1_RST_HWARE 0xf5720001
#define RST_HWARE FLDx(F0900_P1_RST_HWARE)

/*P1_TSCFGM*/
#define R0900_P1_TSCFGM 0xf573
#define TSCFGM REGx(R0900_P1_TSCFGM)
#define F0900_P1_TSFIFO_MANSPEED 0xf57300c0
#define F0900_P1_TSFIFO_PERMDATA 0xf5730020
#define F0900_P1_TSFIFO_DPUNACT 0xf5730002
#define F0900_P1_TSFIFO_INVDATA 0xf5730001

/*P1_TSCFGL*/
#define R0900_P1_TSCFGL 0xf574
#define TSCFGL REGx(R0900_P1_TSCFGL)
#define F0900_P1_TSFIFO_BCLKDEL1CK 0xf57400c0
#define F0900_P1_BCHERROR_MODE 0xf5740030
#define F0900_P1_TSFIFO_NSGNL2DATA 0xf5740008
#define F0900_P1_TSFIFO_EMBINDVB 0xf5740004
#define F0900_P1_TSFIFO_BITSPEED 0xf5740003

/*P1_TSINSDELH*/
#define R0900_P1_TSINSDELH 0xf576
#define TSINSDELH REGx(R0900_P1_TSINSDELH)
#define F0900_P1_TSDEL_SYNCBYTE 0xf5760080
#define F0900_P1_TSDEL_XXHEADER 0xf5760040
#define F0900_P1_TSDEL_BBHEADER 0xf5760020
#define F0900_P1_TSDEL_DATAFIELD 0xf5760010
#define F0900_P1_TSINSDEL_ISCR 0xf5760008
#define F0900_P1_TSINSDEL_NPD 0xf5760004
#define F0900_P1_TSINSDEL_RSPARITY 0xf5760002
#define F0900_P1_TSINSDEL_CRC8 0xf5760001

/*P1_TSDIVN*/
#define R0900_P1_TSDIVN 0xf579
#define TSDIVN REGx(R0900_P1_TSDIVN)
#define F0900_P1_TSFIFO_SPEEDMODE 0xf57900c0

/*P1_TSCFG4*/
#define R0900_P1_TSCFG4 0xf57a
#define TSCFG4 REGx(R0900_P1_TSCFG4)
#define F0900_P1_TSFIFO_TSSPEEDMODE 0xf57a00c0

/*P1_TSSPEED*/
#define R0900_P1_TSSPEED 0xf580
#define TSSPEED REGx(R0900_P1_TSSPEED)
#define F0900_P1_TSFIFO_OUTSPEED 0xf58000ff

/*P1_TSSTATUS*/
#define R0900_P1_TSSTATUS 0xf581
#define TSSTATUS REGx(R0900_P1_TSSTATUS)
#define F0900_P1_TSFIFO_LINEOK 0xf5810080
#define TSFIFO_LINEOK FLDx(F0900_P1_TSFIFO_LINEOK)
#define F0900_P1_TSFIFO_ERROR 0xf5810040
#define F0900_P1_DIL_READY 0xf5810001

/*P1_TSSTATUS2*/
#define R0900_P1_TSSTATUS2 0xf582
#define TSSTATUS2 REGx(R0900_P1_TSSTATUS2)
#define F0900_P1_TSFIFO_DEMODSEL 0xf5820080
#define F0900_P1_TSFIFOSPEED_STORE 0xf5820040
#define F0900_P1_DILXX_RESET 0xf5820020
#define F0900_P1_TSSERIAL_IMPOS 0xf5820010
#define F0900_P1_SCRAMBDETECT 0xf5820002

/*P1_TSBITRATE1*/
#define R0900_P1_TSBITRATE1 0xf583
#define TSBITRATE1 REGx(R0900_P1_TSBITRATE1)
#define F0900_P1_TSFIFO_BITRATE1 0xf58300ff

/*P1_TSBITRATE0*/
#define R0900_P1_TSBITRATE0 0xf584
#define TSBITRATE0 REGx(R0900_P1_TSBITRATE0)
#define F0900_P1_TSFIFO_BITRATE0 0xf58400ff

/*P1_ERRCTRL1*/
#define R0900_P1_ERRCTRL1 0xf598
#define ERRCTRL1 REGx(R0900_P1_ERRCTRL1)
#define F0900_P1_ERR_SOURCE1 0xf59800f0
#define F0900_P1_NUM_EVENT1 0xf5980007

/*P1_ERRCNT12*/
#define R0900_P1_ERRCNT12 0xf599
#define ERRCNT12 REGx(R0900_P1_ERRCNT12)
#define F0900_P1_ERRCNT1_OLDVALUE 0xf5990080
#define F0900_P1_ERR_CNT12 0xf599007f
#define ERR_CNT12 FLDx(F0900_P1_ERR_CNT12)

/*P1_ERRCNT11*/
#define R0900_P1_ERRCNT11 0xf59a
#define ERRCNT11 REGx(R0900_P1_ERRCNT11)
#define F0900_P1_ERR_CNT11 0xf59a00ff
#define ERR_CNT11 FLDx(F0900_P1_ERR_CNT11)

/*P1_ERRCNT10*/
#define R0900_P1_ERRCNT10 0xf59b
#define ERRCNT10 REGx(R0900_P1_ERRCNT10)
#define F0900_P1_ERR_CNT10 0xf59b00ff
#define ERR_CNT10 FLDx(F0900_P1_ERR_CNT10)

/*P1_ERRCTRL2*/
#define R0900_P1_ERRCTRL2 0xf59c
#define ERRCTRL2 REGx(R0900_P1_ERRCTRL2)
#define F0900_P1_ERR_SOURCE2 0xf59c00f0
#define F0900_P1_NUM_EVENT2 0xf59c0007

/*P1_ERRCNT22*/
#define R0900_P1_ERRCNT22 0xf59d
#define ERRCNT22 REGx(R0900_P1_ERRCNT22)
#define F0900_P1_ERRCNT2_OLDVALUE 0xf59d0080
#define F0900_P1_ERR_CNT22 0xf59d007f
#define ERR_CNT22 FLDx(F0900_P1_ERR_CNT22)

/*P1_ERRCNT21*/
#define R0900_P1_ERRCNT21 0xf59e
#define ERRCNT21 REGx(R0900_P1_ERRCNT21)
#define F0900_P1_ERR_CNT21 0xf59e00ff
#define ERR_CNT21 FLDx(F0900_P1_ERR_CNT21)

/*P1_ERRCNT20*/
#define R0900_P1_ERRCNT20 0xf59f
#define ERRCNT20 REGx(R0900_P1_ERRCNT20)
#define F0900_P1_ERR_CNT20 0xf59f00ff
#define ERR_CNT20 FLDx(F0900_P1_ERR_CNT20)

/*P1_FECSPY*/
#define R0900_P1_FECSPY 0xf5a0
#define FECSPY REGx(R0900_P1_FECSPY)
#define F0900_P1_SPY_ENABLE 0xf5a00080
#define F0900_P1_NO_SYNCBYTE 0xf5a00040
#define F0900_P1_SERIAL_MODE 0xf5a00020
#define F0900_P1_UNUSUAL_PACKET 0xf5a00010
#define F0900_P1_BERMETER_DATAMODE 0xf5a00008
#define F0900_P1_BERMETER_LMODE 0xf5a00002
#define F0900_P1_BERMETER_RESET 0xf5a00001

/*P1_FSPYCFG*/
#define R0900_P1_FSPYCFG 0xf5a1
#define FSPYCFG REGx(R0900_P1_FSPYCFG)
#define F0900_P1_FECSPY_INPUT 0xf5a100c0
#define F0900_P1_RST_ON_ERROR 0xf5a10020
#define F0900_P1_ONE_SHOT 0xf5a10010
#define F0900_P1_I2C_MODE 0xf5a1000c
#define F0900_P1_SPY_HYSTERESIS 0xf5a10003

/*P1_FSPYDATA*/
#define R0900_P1_FSPYDATA 0xf5a2
#define FSPYDATA REGx(R0900_P1_FSPYDATA)
#define F0900_P1_SPY_STUFFING 0xf5a20080
#define F0900_P1_SPY_CNULLPKT 0xf5a20020
#define F0900_P1_SPY_OUTDATA_MODE 0xf5a2001f

/*P1_FSPYOUT*/
#define R0900_P1_FSPYOUT 0xf5a3
#define FSPYOUT REGx(R0900_P1_FSPYOUT)
#define F0900_P1_FSPY_DIRECT 0xf5a30080
#define F0900_P1_STUFF_MODE 0xf5a30007

/*P1_FSTATUS*/
#define R0900_P1_FSTATUS 0xf5a4
#define FSTATUS REGx(R0900_P1_FSTATUS)
#define F0900_P1_SPY_ENDSIM 0xf5a40080
#define F0900_P1_VALID_SIM 0xf5a40040
#define F0900_P1_FOUND_SIGNAL 0xf5a40020
#define F0900_P1_DSS_SYNCBYTE 0xf5a40010
#define F0900_P1_RESULT_STATE 0xf5a4000f

/*P1_FBERCPT4*/
#define R0900_P1_FBERCPT4 0xf5a8
#define FBERCPT4 REGx(R0900_P1_FBERCPT4)
#define F0900_P1_FBERMETER_CPT4 0xf5a800ff

/*P1_FBERCPT3*/
#define R0900_P1_FBERCPT3 0xf5a9
#define FBERCPT3 REGx(R0900_P1_FBERCPT3)
#define F0900_P1_FBERMETER_CPT3 0xf5a900ff

/*P1_FBERCPT2*/
#define R0900_P1_FBERCPT2 0xf5aa
#define FBERCPT2 REGx(R0900_P1_FBERCPT2)
#define F0900_P1_FBERMETER_CPT2 0xf5aa00ff

/*P1_FBERCPT1*/
#define R0900_P1_FBERCPT1 0xf5ab
#define FBERCPT1 REGx(R0900_P1_FBERCPT1)
#define F0900_P1_FBERMETER_CPT1 0xf5ab00ff

/*P1_FBERCPT0*/
#define R0900_P1_FBERCPT0 0xf5ac
#define FBERCPT0 REGx(R0900_P1_FBERCPT0)
#define F0900_P1_FBERMETER_CPT0 0xf5ac00ff

/*P1_FBERERR2*/
#define R0900_P1_FBERERR2 0xf5ad
#define FBERERR2 REGx(R0900_P1_FBERERR2)
#define F0900_P1_FBERMETER_ERR2 0xf5ad00ff

/*P1_FBERERR1*/
#define R0900_P1_FBERERR1 0xf5ae
#define FBERERR1 REGx(R0900_P1_FBERERR1)
#define F0900_P1_FBERMETER_ERR1 0xf5ae00ff

/*P1_FBERERR0*/
#define R0900_P1_FBERERR0 0xf5af
#define FBERERR0 REGx(R0900_P1_FBERERR0)
#define F0900_P1_FBERMETER_ERR0 0xf5af00ff

/*P1_FSPYBER*/
#define R0900_P1_FSPYBER 0xf5b2
#define FSPYBER REGx(R0900_P1_FSPYBER)
#define F0900_P1_FSPYBER_SYNCBYTE 0xf5b20010
#define F0900_P1_FSPYBER_UNSYNC 0xf5b20008
#define F0900_P1_FSPYBER_CTIME 0xf5b20007

/*RCCFG2*/
#define R0900_RCCFG2 0xf600

/*TSGENERAL*/
#define R0900_TSGENERAL 0xf630
#define F0900_TSFIFO_DISTS2PAR 0xf6300040
#define F0900_MUXSTREAM_OUTMODE 0xf6300008
#define F0900_TSFIFO_PERMPARAL 0xf6300006

/*TSGENERAL1X*/
#define R0900_TSGENERAL1X 0xf670

/*NBITER_NF4*/
#define R0900_NBITER_NF4 0xfa03
#define F0900_NBITER_NF_QP_1_2 0xfa0300ff

/*NBITER_NF5*/
#define R0900_NBITER_NF5 0xfa04
#define F0900_NBITER_NF_QP_3_5 0xfa0400ff

/*NBITER_NF6*/
#define R0900_NBITER_NF6 0xfa05
#define F0900_NBITER_NF_QP_2_3 0xfa0500ff

/*NBITER_NF7*/
#define R0900_NBITER_NF7 0xfa06
#define F0900_NBITER_NF_QP_3_4 0xfa0600ff

/*NBITER_NF8*/
#define R0900_NBITER_NF8 0xfa07
#define F0900_NBITER_NF_QP_4_5 0xfa0700ff

/*NBITER_NF9*/
#define R0900_NBITER_NF9 0xfa08
#define F0900_NBITER_NF_QP_5_6 0xfa0800ff

/*NBITER_NF10*/
#define R0900_NBITER_NF10 0xfa09
#define F0900_NBITER_NF_QP_8_9 0xfa0900ff

/*NBITER_NF11*/
#define R0900_NBITER_NF11 0xfa0a
#define F0900_NBITER_NF_QP_9_10 0xfa0a00ff

/*NBITER_NF12*/
#define R0900_NBITER_NF12 0xfa0b
#define F0900_NBITER_NF_8P_3_5 0xfa0b00ff

/*NBITER_NF13*/
#define R0900_NBITER_NF13 0xfa0c
#define F0900_NBITER_NF_8P_2_3 0xfa0c00ff

/*NBITER_NF14*/
#define R0900_NBITER_NF14 0xfa0d
#define F0900_NBITER_NF_8P_3_4 0xfa0d00ff

/*NBITER_NF15*/
#define R0900_NBITER_NF15 0xfa0e
#define F0900_NBITER_NF_8P_5_6 0xfa0e00ff

/*NBITER_NF16*/
#define R0900_NBITER_NF16 0xfa0f
#define F0900_NBITER_NF_8P_8_9 0xfa0f00ff

/*NBITER_NF17*/
#define R0900_NBITER_NF17 0xfa10
#define F0900_NBITER_NF_8P_9_10 0xfa1000ff

/*NBITERNOERR*/
#define R0900_NBITERNOERR 0xfa3f
#define F0900_NBITER_STOP_CRIT 0xfa3f000f

/*GAINLLR_NF4*/
#define R0900_GAINLLR_NF4 0xfa43
#define F0900_GAINLLR_NF_QP_1_2 0xfa43007f

/*GAINLLR_NF5*/
#define R0900_GAINLLR_NF5 0xfa44
#define F0900_GAINLLR_NF_QP_3_5 0xfa44007f

/*GAINLLR_NF6*/
#define R0900_GAINLLR_NF6 0xfa45
#define F0900_GAINLLR_NF_QP_2_3 0xfa45007f

/*GAINLLR_NF7*/
#define R0900_GAINLLR_NF7 0xfa46
#define F0900_GAINLLR_NF_QP_3_4 0xfa46007f

/*GAINLLR_NF8*/
#define R0900_GAINLLR_NF8 0xfa47
#define F0900_GAINLLR_NF_QP_4_5 0xfa47007f

/*GAINLLR_NF9*/
#define R0900_GAINLLR_NF9 0xfa48
#define F0900_GAINLLR_NF_QP_5_6 0xfa48007f

/*GAINLLR_NF10*/
#define R0900_GAINLLR_NF10 0xfa49
#define F0900_GAINLLR_NF_QP_8_9 0xfa49007f

/*GAINLLR_NF11*/
#define R0900_GAINLLR_NF11 0xfa4a
#define F0900_GAINLLR_NF_QP_9_10 0xfa4a007f

/*GAINLLR_NF12*/
#define R0900_GAINLLR_NF12 0xfa4b
#define F0900_GAINLLR_NF_8P_3_5 0xfa4b007f

/*GAINLLR_NF13*/
#define R0900_GAINLLR_NF13 0xfa4c
#define F0900_GAINLLR_NF_8P_2_3 0xfa4c007f

/*GAINLLR_NF14*/
#define R0900_GAINLLR_NF14 0xfa4d
#define F0900_GAINLLR_NF_8P_3_4 0xfa4d007f

/*GAINLLR_NF15*/
#define R0900_GAINLLR_NF15 0xfa4e
#define F0900_GAINLLR_NF_8P_5_6 0xfa4e007f

/*GAINLLR_NF16*/
#define R0900_GAINLLR_NF16 0xfa4f
#define F0900_GAINLLR_NF_8P_8_9 0xfa4f007f

/*GAINLLR_NF17*/
#define R0900_GAINLLR_NF17 0xfa50
#define F0900_GAINLLR_NF_8P_9_10 0xfa50007f

/*CFGEXT*/
#define R0900_CFGEXT 0xfa80
#define F0900_STAGMODE 0xfa800080
#define F0900_BYPBCH 0xfa800040
#define F0900_BYPLDPC 0xfa800020
#define F0900_LDPCMODE 0xfa800010
#define F0900_INVLLRSIGN 0xfa800008
#define F0900_SHORTMULT 0xfa800004
#define F0900_EXTERNTX 0xfa800001

/*GENCFG*/
#define R0900_GENCFG 0xfa86
#define F0900_BROADCAST 0xfa860010
#define F0900_PRIORITY 0xfa860002
#define F0900_DDEMOD 0xfa860001

/*LDPCERR1*/
#define R0900_LDPCERR1 0xfa96
#define F0900_LDPC_ERRORS_COUNTER1 0xfa9600ff

/*LDPCERR0*/
#define R0900_LDPCERR0 0xfa97
#define F0900_LDPC_ERRORS_COUNTER0 0xfa9700ff

/*BCHERR*/
#define R0900_BCHERR 0xfa98
#define F0900_ERRORFLAG 0xfa980010
#define F0900_BCH_ERRORS_COUNTER 0xfa98000f

/*TSTRES0*/
#define R0900_TSTRES0 0xff11
#define F0900_FRESFEC 0xff110080

/*P2_TCTL4*/
#define R0900_P2_TCTL4 0xff28
#define F0900_P2_PN4_SELECT 0xff280020

/*P1_TCTL4*/
#define R0900_P1_TCTL4 0xff48
#define TCTL4 shiftx(R0900_P1_TCTL4, demod, 0x20)
#define F0900_P1_PN4_SELECT 0xff480020

/*P2_TSTDISRX*/
#define R0900_P2_TSTDISRX 0xff65
#define F0900_P2_PIN_SELECT1 0xff650008

/*P1_TSTDISRX*/
#define R0900_P1_TSTDISRX 0xff67
#define TSTDISRX shiftx(R0900_P1_TSTDISRX, demod, 2)
#define F0900_P1_PIN_SELECT1 0xff670008
#define PIN_SELECT1 shiftx(F0900_P1_PIN_SELECT1, demod, 0x20000)

#define STV0900_NBREGS 723
#define STV0900_NBFIELDS 1420

#endif

